## TMS Smart

Our engine can be also installed using [TMS Smart Setup](https://github.com/tmssoftware/smartsetup) for Delphi.

This installs our packages in Delphi(s), with benefits being:

- more automatic (command-line to get and install),
- support for installing in multiple Delphi versions at the same time (as it automatically adds the suitable _libsuffix_, which our own packages don't do yet -- see below for details why).
- For more details see [TMS blog post]((https://www.tmssoftware.com/site/blog.asp?post=1146)) and [GH issue where we talk about it](https://github.com/castle-engine/castle-engine/issues/678#issuecomment-3000913493).

The drawback is that this doesn't install our tools (like [editor](https://castle-engine.io/editor) or [build tool](https://castle-engine.io/build_tool)). As such, it's not the advised way to install our engine right now for most users (better follow our [installation instructions for Delphi](https://castle-engine.io/delphi_packages)).

However, it may be great to automated installation done by _continuous integration/delivery_, like in [GitHub Actions](https://castle-engine.io/github_actions).

Short instructions how to do this:

1. Get https://github.com/tmssoftware/smartsetup , with submodules, in branch `git-registry`. Command-line:

    ```
    git clone https://github.com/tmssoftware/smartsetup
    cd smartsetup
    git checkout git-registry
    git submodule update --init --recursive # get submodules in externals/
    ```

2. Build and move the `.../bin/Win64/Debug/tms.exe` binary anywhere to `$PATH` on your system.
3. Create and enter and directory to host the configuration, like `c:/smartsetup-test`. Enter it from the command-line and execute following commands inside it:
4. `tms config` (it will open YAML file in your code editor; in principle you don't need to tweak it, the default config should work out-of-the-box)
5. `tms server-enable community`
    - Trouble? Do `tms install castle.engine -verbose`