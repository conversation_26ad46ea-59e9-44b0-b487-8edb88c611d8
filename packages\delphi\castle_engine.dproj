<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{6A77C25F-C335-4381-9B5A-FB362258212C}</ProjectGuid>
        <MainSource>castle_engine.dpk</MainSource>
        <ProjectVersion>19.5</ProjectVersion>
        <FrameworkType>None</FrameworkType>
        <Base>True</Base>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <TargetedPlatforms>131</TargetedPlatforms>
        <AppType>Package</AppType>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Linux64' and '$(Base)'=='true') or '$(Base_Linux64)'!=''">
        <Base_Linux64>true</Base_Linux64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win64)'!=''">
        <Cfg_1_Win64>true</Cfg_1_Win64>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_E>false</DCC_E>
        <DCC_N>false</DCC_N>
        <DCC_S>false</DCC_S>
        <DCC_F>false</DCC_F>
        <DCC_K>false</DCC_K>
        <GenDll>true</GenDll>
        <GenPackage>true</GenPackage>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;$(DCC_Namespace)</DCC_Namespace>
        <DCC_CBuilderOutput>All</DCC_CBuilderOutput>
        <SanitizedProjectName>castle_engine</SanitizedProjectName>
        <DCC_UnitSearchPath>../../src/base;../../src/common_includes;../../src/base/android;../../src/base/windows;../../src/base/unix;../../src/base_rendering;../../src/base_rendering/dglopengl;../../src/base_rendering/glsl/generated-pascal;../../src/fonts;../../src/window;../../src/window/gtk;../../src/window/windows;../../src/window/unix;../../src/window/deprecated_units;../../src/images;../../src/transform;../../src/scene;../../src/scene/glsl/generated-pascal;../../src/scene/x3d;../../src/scene/load;../../src/scene/load/spine;../../src/scene/load/md3;../../src/scene/load/ifc;../../src/scene/load/collada;../../src/scene/load/pasgltf;../../src/audio;../../src/audio/fmod;../../src/audio/openal;../../src/audio/ogg_vorbis;../../src/files;../../src/files/indy;../../src/castlescript;../../src/ui;../../src/ui/windows;../../src/services;../../src/services/steam;../../src/physics;../../src/physics/kraft;../../src/deprecated_units;../../src/vampyre_imaginglib/src/Source;../../src/vampyre_imaginglib/src/Source/JpegLib;../../src/vampyre_imaginglib/src/Source/ZLib;../../src/vampyre_imaginglib/src/Extras/Extensions;../../src/vampyre_imaginglib/src/Extensions/J2KObjects;../../src/vampyre_imaginglib/src/Extensions/LibTiff;../../src/vampyre_imaginglib/src/Extensions;../../src/compatibility/delphi-only;../../src/compatibility/delphi-only/fcl-json;../../src/delphi;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <VerInfo_Locale>1045</VerInfo_Locale>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <DCC_Description>Castle Game Engine - base for other packages</DCC_Description>
        <DCC_Define>CASTLE_DELPHI_PACKAGE;$(DCC_Define)</DCC_Define>
        <DCC_DUPLICATE_CTOR_DTOR>false</DCC_DUPLICATE_CTOR_DTOR>
        <DCC_UNSUPPORTED_CONSTRUCT>false</DCC_UNSUPPORTED_CONSTRUCT>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Linux64)'!=''">
        <DCC_UsePackage>rtl;xmlrtl;IndySystem;IndyProtocols;IndyCore;$(DCC_UsePackage)</DCC_UsePackage>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_UsePackage>rtl;xmlrtl;IndySystem;IndyProtocols;IndyCore;$(DCC_UsePackage)</DCC_UsePackage>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <DCC_UsePackage>rtl;xmlrtl;IndySystem;IndyProtocols;IndyCore;$(DCC_UsePackage)</DCC_UsePackage>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;$(DCC_Namespace)</DCC_Namespace>
        <BT_BuildType>Debug</BT_BuildType>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
        <DCC_IntegerOverflowCheck>true</DCC_IntegerOverflowCheck>
        <DCC_RangeChecking>true</DCC_RangeChecking>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_Description>Castle Game Engine - base</DCC_Description>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win64)'!=''">
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <DCC_Description>Castle Game Engine - base</DCC_Description>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_DebugInformation>0</DCC_DebugInformation>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="rtl.dcp"/>
        <DCCReference Include="xmlrtl.dcp"/>
        <DCCReference Include="IndySystem.dcp"/>
        <DCCReference Include="IndyProtocols.dcp"/>
        <DCCReference Include="IndyCore.dcp"/>
        <DCCReference Include="..\..\src\delphi\castlecontrolcontainer.pas"/>
        <DCCReference Include="..\..\src\delphi\castleinternaldelphiutils.pas"/>
        <DCCReference Include="..\..\src\base\castleapplicationproperties.pas"/>
        <DCCReference Include="..\..\src\base\castleclassutils.pas"/>
        <DCCReference Include="..\..\src\base\castlecolors.pas"/>
        <DCCReference Include="..\..\src\base\castledynlib.pas"/>
        <DCCReference Include="..\..\src\base\castleinternalclassutils.pas"/>
        <DCCReference Include="..\..\src\base\castleinternalrttiutils.pas"/>
        <DCCReference Include="..\..\src\base\castleinternalzlib.pas"/>
        <DCCReference Include="..\..\src\base\castleinternalzstream.pas"/>
        <DCCReference Include="..\..\src\base\castlelog.pas"/>
        <DCCReference Include="..\..\src\base\castlemessaging.pas"/>
        <DCCReference Include="..\..\src\base\castleparameters.pas"/>
        <DCCReference Include="..\..\src\base\castleprojection.pas"/>
        <DCCReference Include="..\..\src\base\castlequaternions.pas"/>
        <DCCReference Include="..\..\src\base\castlerectangles.pas"/>
        <DCCReference Include="..\..\src\base\castlerenderoptions.pas"/>
        <DCCReference Include="..\..\src\base\castlestreamutils.pas"/>
        <DCCReference Include="..\..\src\base\castlestringutils.pas"/>
        <DCCReference Include="..\..\src\base\castletimeutils.pas"/>
        <DCCReference Include="..\..\src\base\castleunicode.pas"/>
        <DCCReference Include="..\..\src\base\castleutils.pas"/>
        <DCCReference Include="..\..\src\base\castlevectors.pas"/>
        <DCCReference Include="..\..\src\base\castlevectorsinternaldouble.pas"/>
        <DCCReference Include="..\..\src\base\castlevectorsinternalsingle.pas"/>
        <DCCReference Include="..\..\src\audio\castleinternalabstractsoundbackend.pas"/>
        <DCCReference Include="..\..\src\audio\castleinternalsoundfile.pas"/>
        <DCCReference Include="..\..\src\audio\castlesoundbase.pas"/>
        <DCCReference Include="..\..\src\audio\castlesoundengine.pas"/>
        <DCCReference Include="..\..\src\files\castlecomponentserialize.pas"/>
        <DCCReference Include="..\..\src\files\castleconfig.pas"/>
        <DCCReference Include="..\..\src\files\castledownload.pas"/>
        <DCCReference Include="..\..\src\files\castlefilefilters.pas"/>
        <DCCReference Include="..\..\src\files\castlefilesutils.pas"/>
        <DCCReference Include="..\..\src\files\castlefindfiles.pas"/>
        <DCCReference Include="..\..\src\files\castleinternaldatauri.pas"/>
        <DCCReference Include="..\..\src\files\castleinternaldirectoryinformation.pas"/>
        <DCCReference Include="..\..\src\files\castlerecentfiles.pas"/>
        <DCCReference Include="..\..\src\files\castleuriutils.pas"/>
        <DCCReference Include="..\..\src\files\castlexmlcfginternal.pas"/>
        <DCCReference Include="..\..\src\files\castlexmlconfig.pas"/>
        <DCCReference Include="..\..\src\files\castlexmlutils.pas"/>
        <DCCReference Include="..\..\src\files\castlezip.pas"/>
        <DCCReference Include="..\..\src\scene\x3d\x3dfields.pas"/>
        <DCCReference Include="..\..\src\scene\x3d\x3dnodes.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlecurves.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescript.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescriptcorefunctions.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescriptlexer.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescriptparser.pas"/>
        <DCCReference Include="..\..\src\scene\load\castleloadgltf.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dload.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternal3ds.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalcocos2d.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalgeo.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalgltf.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalimage.pas"/>
        <DCCReference Include="..\..\src\scene\load\md3\x3dloadinternalmd3.pas"/>
        <DCCReference Include="..\..\src\scene\load\ifc\castleifc.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalobj.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalstl.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternaltiledmap.pas"/>
        <DCCReference Include="..\..\src\scene\load\x3dloadinternalutils.pas"/>
        <DCCReference Include="..\..\src\scene\load\collada\x3dloadinternalcollada.pas"/>
        <DCCReference Include="..\..\src\scene\load\spine\x3dloadinternalspine.pas"/>
        <DCCReference Include="..\..\src\audio\openal\castleinternalalutils.pas"/>
        <DCCReference Include="..\..\src\audio\openal\castleinternalefx.pas"/>
        <DCCReference Include="..\..\src\audio\openal\castleinternalopenal.pas"/>
        <DCCReference Include="..\..\src\audio\openal\castleopenalsoundbackend.pas"/>
        <DCCReference Include="..\..\src\transform\castlebehaviors.pas"/>
        <DCCReference Include="..\..\src\transform\castleboxes.pas"/>
        <DCCReference Include="..\..\src\transform\castlecameras.pas"/>
        <DCCReference Include="..\..\src\transform\castlefrustum.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalbasetriangleoctree.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalcubemaps.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalgeometryarrays.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalglshadowvolumes.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalnurbs.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternaloctree.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalphysicsvisualization.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalrays.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalspacefillingcurves.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalspheresampling.pas"/>
        <DCCReference Include="..\..\src\transform\castleinternalsphericalharmonics.pas"/>
        <DCCReference Include="..\..\src\transform\castlesectors.pas"/>
        <DCCReference Include="..\..\src\transform\castletransform.pas"/>
        <DCCReference Include="..\..\src\transform\castletriangles.pas"/>
        <DCCReference Include="..\..\src\transform\castletriangulate.pas"/>
        <DCCReference Include="..\..\src\services\castleactivityrecognition.pas"/>
        <DCCReference Include="..\..\src\services\castleads.pas"/>
        <DCCReference Include="..\..\src\services\castleanalytics.pas"/>
        <DCCReference Include="..\..\src\services\castlefacebook.pas"/>
        <DCCReference Include="..\..\src\services\castlegameservice.pas"/>
        <DCCReference Include="..\..\src\services\castlehelpshift.pas"/>
        <DCCReference Include="..\..\src\services\castleinapppurchases.pas"/>
        <DCCReference Include="..\..\src\services\castleopendocument.pas"/>
        <DCCReference Include="..\..\src\services\castlephotoservice.pas"/>
        <DCCReference Include="..\..\src\services\castletenjin.pas"/>
        <DCCReference Include="..\..\src\services\castletestfairy.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\dzlib.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\imadler.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\iminfblock.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\iminfcodes.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\iminffast.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\iminftrees.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\iminfutil.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\impaszlib.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\imtrees.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\imzdeflate.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\imzinflate.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ZLib\imzutil.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\Imaging.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingBitmap.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingCanvases.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingClasses.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingColors.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingComponents.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingDds.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingFormats.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingGif.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingIO.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingJpeg.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingNetworkGraphics.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingPortableMaps.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingRadiance.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingTarga.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingTypes.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\ImagingUtility.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcapimin.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcapistd.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjccoefct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjccolor.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcdctmgr.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjchuff.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcinit.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcmainct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcmarker.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcmaster.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcomapi.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcparam.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcphuff.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcprepct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcsample.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdapimin.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdapistd.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdcoefct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdcolor.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjddctmgr.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdeferr.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdhuff.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdinput.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmainct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmarker.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmaster.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmerge.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdphuff.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdpostct.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdsample.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjerror.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjfdctflt.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjfdctfst.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjfdctint.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctflt.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctfst.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctint.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctred.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjinclude.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjmemmgr.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjmemnobs.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjmorecfg.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjpeglib.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjquant1.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjquant2.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjutils.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Extensions\ImagingExtFileFormats.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\base64.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\ctypes.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\custapp.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\dom.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\FPHashCompatibility.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\singleinstance.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\uriparser.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\xmlread.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\xmlwrite.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\fcl-json\fpjson.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\fcl-json\fpjsonrtti.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\fcl-json\jsonparser.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\fcl-json\jsonreader.pas"/>
        <DCCReference Include="..\..\src\compatibility\delphi-only\fcl-json\jsonscanner.pas"/>
        <DCCReference Include="..\..\src\scene\load\pasgltf\CastlePasDblStrUtils.pas"/>
        <DCCReference Include="..\..\src\scene\load\pasgltf\CastlePasGLTF.pas"/>
        <DCCReference Include="..\..\src\scene\load\pasgltf\CastlePasJSON.pas"/>
        <DCCReference Include="..\..\src\ui\castlecontrols.pas"/>
        <DCCReference Include="..\..\src\ui\castledialogviews.pas"/>
        <DCCReference Include="..\..\src\ui\castleflasheffect.pas"/>
        <DCCReference Include="..\..\src\ui\castleinputs.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalcameragestures.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalcontrolsimages.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalinspector.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalgamecontrollersexplicit.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalgamecontrollerswindows.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalpk3dconnexion.pas"/>
        <DCCReference Include="..\..\src\ui\castleinternalsettings.pas"/>
        <DCCReference Include="..\..\src\ui\castlegamecontrollers.pas"/>
        <DCCReference Include="..\..\src\ui\castlekeysmouse.pas"/>
        <DCCReference Include="..\..\src\ui\castlenotifications.pas"/>
        <DCCReference Include="..\..\src\ui\castleuicontrols.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleglimages.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleglshaders.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleglutils.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleglversion.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleinternalglutils.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castlerendercontext.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castlerenderprimitives.pas"/>
        <DCCReference Include="..\..\src\audio\ogg_vorbis\castleinternalogg.pas"/>
        <DCCReference Include="..\..\src\audio\ogg_vorbis\castleinternalvorbiscodec.pas"/>
        <DCCReference Include="..\..\src\audio\ogg_vorbis\castleinternalvorbisdecoder.pas"/>
        <DCCReference Include="..\..\src\audio\ogg_vorbis\castleinternalvorbisfile.pas"/>
        <DCCReference Include="..\..\src\images\castleimages.pas"/>
        <DCCReference Include="..\..\src\images\castleinternalautogenerated.pas"/>
        <DCCReference Include="..\..\src\images\castleinternalcompositeimage.pas"/>
        <DCCReference Include="..\..\src\images\castleinternaldatacompression.pas"/>
        <DCCReference Include="..\..\src\images\castleinternalpng.pas"/>
        <DCCReference Include="..\..\src\images\castletextureimages.pas"/>
        <DCCReference Include="..\..\src\images\castlevideos.pas"/>
        <DCCReference Include="..\..\src\fonts\castlefonts.pas"/>
        <DCCReference Include="..\..\src\fonts\castleinternalfreetype.pas"/>
        <DCCReference Include="..\..\src\fonts\castleinternalfreetypeh.pas"/>
        <DCCReference Include="..\..\src\fonts\castleinternalrichtext.pas"/>
        <DCCReference Include="..\..\src\fonts\castletexturefont_defaultui.pas"/>
        <DCCReference Include="..\..\src\fonts\castletexturefont_default3d_sans.pas"/>
        <DCCReference Include="..\..\src\fonts\castletexturefontdata.pas"/>
        <DCCReference Include="..\..\src\scene\castledebugtransform.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalarraysgenerator.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalbackgroundrenderer.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalbatchshapes.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalglcubemaps.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalmaterialproperties.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalnodeinterpolator.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalnoise.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalnormals.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalprimitivematerial.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalrenderer.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalshadowmaps.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalshapeoctree.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalspritesheet.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternaltriangleoctree.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalx3dlexer.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalx3dscript.pas"/>
        <DCCReference Include="..\..\src\scene\castlerendererinternallights.pas"/>
        <DCCReference Include="..\..\src\scene\castlerendererinternalshader.pas"/>
        <DCCReference Include="..\..\src\scene\castlerendererinternaltextureenv.pas"/>
        <DCCReference Include="..\..\src\scene\castlescene.pas"/>
        <DCCReference Include="..\..\src\scene\castlescenecore.pas"/>
        <DCCReference Include="..\..\src\scene\castlesceneinternalblending.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalocclusionculling.pas"/>
        <DCCReference Include="..\..\src\scene\castlesceneinternalshape.pas"/>
        <DCCReference Include="..\..\src\scene\castlescreeneffects.pas"/>
        <DCCReference Include="..\..\src\scene\castleshapeinternalrendershadowvolumes.pas"/>
        <DCCReference Include="..\..\src\scene\castleshapeinternalshadowvolumes.pas"/>
        <DCCReference Include="..\..\src\scene\castleshapes.pas"/>
        <DCCReference Include="..\..\src\scene\castleterrain.pas"/>
        <DCCReference Include="..\..\src\scene\castlethirdpersonnavigation.pas"/>
        <DCCReference Include="..\..\src\scene\castletiledmap.pas"/>
        <DCCReference Include="..\..\src\scene\castleviewport.pas"/>
        <DCCReference Include="..\..\src\scene\x3dcamerautils.pas"/>
        <DCCReference Include="..\..\src\scene\x3dtime.pas"/>
        <DCCReference Include="..\..\src\scene\castlelivingbehaviors.pas"/>
        <DCCReference Include="..\..\src\physics\kraft\kraft.pas"/>
        <DCCReference Include="..\..\src\physics\kraft\castleinternalkraftoverrides.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Extensions\ImagingXpm.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Extensions\ImagingPcx.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Extensions\ImagingPsd.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescriptarrays.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescriptimages.pas"/>
        <DCCReference Include="..\..\src\castlescript\castlescriptvectors.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalshapesrenderer.pas"/>
        <DCCReference Include="..\..\src\files\castleinternalfilemonitor.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternalscreeneffects.pas"/>
        <DCCReference Include="..\..\src\scene\castletransformmanipulate.pas"/>
        <DCCReference Include="..\..\src\scene\castleinternaltransformdata.pas"/>
        <DCCReference Include="..\..\src\scene\x3d\castleinternalnodesunsupported.pas"/>
        <DCCReference Include="..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctasm.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleinternalcontextbase.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleinternalcontextegl.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleinternalcontextglx.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleinternalcontextwgl.pas"/>
        <DCCReference Include="..\..\src\base_rendering\castleinternalegl.pas"/>
        <DCCReference Include="..\..\src\base_rendering\dglopengl\castlegl.pas"/>
        <DCCReference Include="..\..\src\files\castleinternalurlutils.pas"/>
        <DCCReference Include="..\..\src\services\steam\castleinternalsteamapi.pas"/>
        <DCCReference Include="..\..\src\services\steam\castlesteam.pas"/>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Package</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">castle_engine.dpk</Source>
                </Source>
                <Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\bcboffice2k280.bpl">Embarcadero C++Builder Office 2000 Servers Package</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\bcbofficexp280.bpl">Embarcadero C++Builder Office XP Servers Package</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dcloffice2k280.bpl">Microsoft Office 2000 Sample Automation Server Wrapper Components</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclofficexp280.bpl">Microsoft Office XP Sample Automation Server Wrapper Components</Excluded_Packages>
                </Excluded_Packages>
            </Delphi.Personality>
            <Deployment Version="4">
                <DeployFile LocalName="$(BDS)\Redist\iossimulator\libcgunwind.1.0.dylib" Class="DependencyModule">
                    <Platform Name="iOSSimulator">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="$(BDS)\Redist\iossimulator\libpcre.dylib" Class="DependencyModule">
                    <Platform Name="iOSSimulator">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="$(BDS)\Redist\osx32\libcgunwind.1.0.dylib" Class="DependencyModule">
                    <Platform Name="OSX32">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="$(BDSCOMMONDIR)\Bpl\Win64\castle_engine.bpl" Configuration="Debug" Class="ProjectOutput">
                    <Platform Name="Win64">
                        <RemoteName>castle_engine.bpl</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="$(BDSCOMMONDIR)\Bpl\castle_engine.bpl" Configuration="Debug" Class="ProjectOutput">
                    <Platform Name="Win32">
                        <RemoteName>castle_engine.bpl</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployClass Name="AdditionalDebugSymbols">
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidClasses">
                    <Platform Name="Android">
                        <RemoteDir>classes</RemoteDir>
                        <Operation>64</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>classes</RemoteDir>
                        <Operation>64</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidFileProvider">
                    <Platform Name="Android">
                        <RemoteDir>res\xml</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\xml</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidGDBServer">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeArmeabiFile">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeArmeabiv7aFile">
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeMipsFile">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\mips</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\mips</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidServiceOutput">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\arm64-v8a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidServiceOutput_Android32">
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashImageDef">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashStyles">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashStylesV21">
                    <Platform Name="Android">
                        <RemoteDir>res\values-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values-v21</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_Colors">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_DefaultAppIcon">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon144">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon192">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon36">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-ldpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-ldpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon48">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon72">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon96">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon24">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon36">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon48">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon72">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_NotificationIcon96">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xxxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage426">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-small</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-small</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage470">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-normal</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-normal</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage640">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-large</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-large</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage960">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xlarge</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\drawable-xlarge</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_Strings">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DebugSymbols">
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyFramework">
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyModule">
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.dll;.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="DependencyPackage">
                    <Platform Name="iOSDevice32">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Name="File">
                    <Platform Name="Android">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSDevice32">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectAndroidManifest">
                    <Platform Name="Android">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOSXDebug"/>
                <DeployClass Name="ProjectOSXEntitlements"/>
                <DeployClass Name="ProjectOSXInfoPList"/>
                <DeployClass Name="ProjectOSXResource">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX64">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="ProjectOutput">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\arm64-v8a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Linux64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSX64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="OSXARM64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOutput_Android32">
                    <Platform Name="Android64">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectUWPManifest">
                    <Platform Name="Win32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win64">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSDeviceDebug">
                    <Platform Name="iOSDevice32">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSEntitlements"/>
                <DeployClass Name="ProjectiOSInfoPList"/>
                <DeployClass Name="ProjectiOSLaunchScreen"/>
                <DeployClass Name="ProjectiOSResource">
                    <Platform Name="iOSDevice32">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSDevice64">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo150">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win64">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo44">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win64">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iOS_AppStore1024">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_AppIcon152">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_AppIcon167">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Launch2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_LaunchDark2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Notification40">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Setting58">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_SpotLight80">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_AppIcon120">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_AppIcon180">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch3x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_LaunchDark2x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_LaunchDark3x">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\LaunchScreenImage.imageset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Notification40">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Notification60">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Setting58">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Setting87">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Spotlight120">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Spotlight80">
                    <Platform Name="iOSDevice64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimARM64">
                        <RemoteDir>..\$(PROJECTNAME).launchscreen\Assets\AppIcon.appiconset</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <ProjectRoot Platform="Android" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Android64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="iOSDevice32" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="iOSDevice64" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="iOSSimARM64" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="Linux64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSX32" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSX64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSXARM64" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Win32" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="Win64" Name="$(PROJECTNAME)"/>
            </Deployment>
            <Platforms>
                <Platform value="Linux64">True</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">True</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
</Project>
