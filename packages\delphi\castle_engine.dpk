package castle_engine;

{$R *.res}
{$IFDEF IMPLICITBUILDING This IFDEF should not be used by users}
{$ALIGN 8}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$<PERSON>MPOR<PERSON><PERSON><PERSON><PERSON> ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION OFF}
{$OVERFLOWCHECKS ON}
{$RANGECHECKS ON}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES ON}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DEFINE DEBUG}
{$DEFINE CASTLE_DELPHI_PACKAGE}
{$ENDIF IMPLICITBUILDING}
{$DESCRIPTION 'Castle Game Engine - base'}
{$IMPL<PERSON><PERSON>BUILD ON}

requires
  rtl,
  xmlrtl,
  IndySystem,
  IndyProtocols,
  IndyCore;

contains
  castlecontrolcontainer in '..\..\src\delphi\castlecontrolcontainer.pas',
  castleinternaldelphiutils in '..\..\src\delphi\castleinternaldelphiutils.pas',
  castleapplicationproperties in '..\..\src\base\castleapplicationproperties.pas',
  castleclassutils in '..\..\src\base\castleclassutils.pas',
  castlecolors in '..\..\src\base\castlecolors.pas',
  castledynlib in '..\..\src\base\castledynlib.pas',
  castleinternalclassutils in '..\..\src\base\castleinternalclassutils.pas',
  castleinternalrttiutils in '..\..\src\base\castleinternalrttiutils.pas',
  castleinternalzlib in '..\..\src\base\castleinternalzlib.pas',
  castleinternalzstream in '..\..\src\base\castleinternalzstream.pas',
  castlelog in '..\..\src\base\castlelog.pas',
  castlemessaging in '..\..\src\base\castlemessaging.pas',
  castleparameters in '..\..\src\base\castleparameters.pas',
  castleprojection in '..\..\src\base\castleprojection.pas',
  castlequaternions in '..\..\src\base\castlequaternions.pas',
  castlerectangles in '..\..\src\base\castlerectangles.pas',
  castlerenderoptions in '..\..\src\base\castlerenderoptions.pas',
  castlestreamutils in '..\..\src\base\castlestreamutils.pas',
  castlestringutils in '..\..\src\base\castlestringutils.pas',
  castletimeutils in '..\..\src\base\castletimeutils.pas',
  castleunicode in '..\..\src\base\castleunicode.pas',
  castleutils in '..\..\src\base\castleutils.pas',
  castlevectors in '..\..\src\base\castlevectors.pas',
  castlevectorsinternaldouble in '..\..\src\base\castlevectorsinternaldouble.pas',
  castlevectorsinternalsingle in '..\..\src\base\castlevectorsinternalsingle.pas',
  castleinternalabstractsoundbackend in '..\..\src\audio\castleinternalabstractsoundbackend.pas',
  castleinternalsoundfile in '..\..\src\audio\castleinternalsoundfile.pas',
  castlesoundbase in '..\..\src\audio\castlesoundbase.pas',
  castlesoundengine in '..\..\src\audio\castlesoundengine.pas',
  castlecomponentserialize in '..\..\src\files\castlecomponentserialize.pas',
  castleconfig in '..\..\src\files\castleconfig.pas',
  castledownload in '..\..\src\files\castledownload.pas',
  castlefilefilters in '..\..\src\files\castlefilefilters.pas',
  castlefilesutils in '..\..\src\files\castlefilesutils.pas',
  castlefindfiles in '..\..\src\files\castlefindfiles.pas',
  castleinternaldatauri in '..\..\src\files\castleinternaldatauri.pas',
  castleinternaldirectoryinformation in '..\..\src\files\castleinternaldirectoryinformation.pas',
  castlerecentfiles in '..\..\src\files\castlerecentfiles.pas',
  castleuriutils in '..\..\src\files\castleuriutils.pas',
  castlexmlcfginternal in '..\..\src\files\castlexmlcfginternal.pas',
  castlexmlconfig in '..\..\src\files\castlexmlconfig.pas',
  castlexmlutils in '..\..\src\files\castlexmlutils.pas',
  castlezip in '..\..\src\files\castlezip.pas',
  x3dfields in '..\..\src\scene\x3d\x3dfields.pas',
  x3dnodes in '..\..\src\scene\x3d\x3dnodes.pas',
  castlecurves in '..\..\src\castlescript\castlecurves.pas',
  castlescript in '..\..\src\castlescript\castlescript.pas',
  castlescriptcorefunctions in '..\..\src\castlescript\castlescriptcorefunctions.pas',
  castlescriptlexer in '..\..\src\castlescript\castlescriptlexer.pas',
  castlescriptparser in '..\..\src\castlescript\castlescriptparser.pas',
  castleloadgltf in '..\..\src\scene\load\castleloadgltf.pas',
  x3dload in '..\..\src\scene\load\x3dload.pas',
  x3dloadinternal3ds in '..\..\src\scene\load\x3dloadinternal3ds.pas',
  x3dloadinternalcocos2d in '..\..\src\scene\load\x3dloadinternalcocos2d.pas',
  x3dloadinternalgeo in '..\..\src\scene\load\x3dloadinternalgeo.pas',
  x3dloadinternalgltf in '..\..\src\scene\load\x3dloadinternalgltf.pas',
  x3dloadinternalimage in '..\..\src\scene\load\x3dloadinternalimage.pas',
  x3dloadinternalmd3 in '..\..\src\scene\load\md3\x3dloadinternalmd3.pas',
  castleifc in '..\..\src\scene\load\ifc\castleifc.pas',
  x3dloadinternalobj in '..\..\src\scene\load\x3dloadinternalobj.pas',
  x3dloadinternalstl in '..\..\src\scene\load\x3dloadinternalstl.pas',
  x3dloadinternaltiledmap in '..\..\src\scene\load\x3dloadinternaltiledmap.pas',
  x3dloadinternalutils in '..\..\src\scene\load\x3dloadinternalutils.pas',
  x3dloadinternalcollada in '..\..\src\scene\load\collada\x3dloadinternalcollada.pas',
  x3dloadinternalspine in '..\..\src\scene\load\spine\x3dloadinternalspine.pas',
  castleinternalalutils in '..\..\src\audio\openal\castleinternalalutils.pas',
  castleinternalefx in '..\..\src\audio\openal\castleinternalefx.pas',
  castleinternalopenal in '..\..\src\audio\openal\castleinternalopenal.pas',
  castleopenalsoundbackend in '..\..\src\audio\openal\castleopenalsoundbackend.pas',
  castlebehaviors in '..\..\src\transform\castlebehaviors.pas',
  castleboxes in '..\..\src\transform\castleboxes.pas',
  castlecameras in '..\..\src\transform\castlecameras.pas',
  castlefrustum in '..\..\src\transform\castlefrustum.pas',
  castleinternalbasetriangleoctree in '..\..\src\transform\castleinternalbasetriangleoctree.pas',
  castleinternalcubemaps in '..\..\src\transform\castleinternalcubemaps.pas',
  castleinternalgeometryarrays in '..\..\src\transform\castleinternalgeometryarrays.pas',
  castleinternalglshadowvolumes in '..\..\src\transform\castleinternalglshadowvolumes.pas',
  castleinternalnurbs in '..\..\src\transform\castleinternalnurbs.pas',
  castleinternaloctree in '..\..\src\transform\castleinternaloctree.pas',
  castleinternalphysicsvisualization in '..\..\src\transform\castleinternalphysicsvisualization.pas',
  castleinternalrays in '..\..\src\transform\castleinternalrays.pas',
  castleinternalspacefillingcurves in '..\..\src\transform\castleinternalspacefillingcurves.pas',
  castleinternalspheresampling in '..\..\src\transform\castleinternalspheresampling.pas',
  castleinternalsphericalharmonics in '..\..\src\transform\castleinternalsphericalharmonics.pas',
  castlesectors in '..\..\src\transform\castlesectors.pas',
  castletransform in '..\..\src\transform\castletransform.pas',
  castletriangles in '..\..\src\transform\castletriangles.pas',
  castletriangulate in '..\..\src\transform\castletriangulate.pas',
  castleactivityrecognition in '..\..\src\services\castleactivityrecognition.pas',
  castleads in '..\..\src\services\castleads.pas',
  castleanalytics in '..\..\src\services\castleanalytics.pas',
  castlefacebook in '..\..\src\services\castlefacebook.pas',
  castlegameservice in '..\..\src\services\castlegameservice.pas',
  castlehelpshift in '..\..\src\services\castlehelpshift.pas',
  castleinapppurchases in '..\..\src\services\castleinapppurchases.pas',
  castleopendocument in '..\..\src\services\castleopendocument.pas',
  castlephotoservice in '..\..\src\services\castlephotoservice.pas',
  castletenjin in '..\..\src\services\castletenjin.pas',
  castletestfairy in '..\..\src\services\castletestfairy.pas',
  dzlib in '..\..\src\vampyre_imaginglib\src\Source\ZLib\dzlib.pas',
  imadler in '..\..\src\vampyre_imaginglib\src\Source\ZLib\imadler.pas',
  iminfblock in '..\..\src\vampyre_imaginglib\src\Source\ZLib\iminfblock.pas',
  iminfcodes in '..\..\src\vampyre_imaginglib\src\Source\ZLib\iminfcodes.pas',
  iminffast in '..\..\src\vampyre_imaginglib\src\Source\ZLib\iminffast.pas',
  iminftrees in '..\..\src\vampyre_imaginglib\src\Source\ZLib\iminftrees.pas',
  iminfutil in '..\..\src\vampyre_imaginglib\src\Source\ZLib\iminfutil.pas',
  impaszlib in '..\..\src\vampyre_imaginglib\src\Source\ZLib\impaszlib.pas',
  imtrees in '..\..\src\vampyre_imaginglib\src\Source\ZLib\imtrees.pas',
  imzdeflate in '..\..\src\vampyre_imaginglib\src\Source\ZLib\imzdeflate.pas',
  imzinflate in '..\..\src\vampyre_imaginglib\src\Source\ZLib\imzinflate.pas',
  imzutil in '..\..\src\vampyre_imaginglib\src\Source\ZLib\imzutil.pas',
  Imaging in '..\..\src\vampyre_imaginglib\src\Source\Imaging.pas',
  ImagingBitmap in '..\..\src\vampyre_imaginglib\src\Source\ImagingBitmap.pas',
  ImagingCanvases in '..\..\src\vampyre_imaginglib\src\Source\ImagingCanvases.pas',
  ImagingClasses in '..\..\src\vampyre_imaginglib\src\Source\ImagingClasses.pas',
  ImagingColors in '..\..\src\vampyre_imaginglib\src\Source\ImagingColors.pas',
  ImagingComponents in '..\..\src\vampyre_imaginglib\src\Source\ImagingComponents.pas',
  ImagingDds in '..\..\src\vampyre_imaginglib\src\Source\ImagingDds.pas',
  ImagingFormats in '..\..\src\vampyre_imaginglib\src\Source\ImagingFormats.pas',
  ImagingGif in '..\..\src\vampyre_imaginglib\src\Source\ImagingGif.pas',
  ImagingIO in '..\..\src\vampyre_imaginglib\src\Source\ImagingIO.pas',
  ImagingJpeg in '..\..\src\vampyre_imaginglib\src\Source\ImagingJpeg.pas',
  ImagingNetworkGraphics in '..\..\src\vampyre_imaginglib\src\Source\ImagingNetworkGraphics.pas',
  ImagingPortableMaps in '..\..\src\vampyre_imaginglib\src\Source\ImagingPortableMaps.pas',
  ImagingRadiance in '..\..\src\vampyre_imaginglib\src\Source\ImagingRadiance.pas',
  ImagingTarga in '..\..\src\vampyre_imaginglib\src\Source\ImagingTarga.pas',
  ImagingTypes in '..\..\src\vampyre_imaginglib\src\Source\ImagingTypes.pas',
  ImagingUtility in '..\..\src\vampyre_imaginglib\src\Source\ImagingUtility.pas',
  imjcapimin in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcapimin.pas',
  imjcapistd in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcapistd.pas',
  imjccoefct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjccoefct.pas',
  imjccolor in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjccolor.pas',
  imjcdctmgr in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcdctmgr.pas',
  imjchuff in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjchuff.pas',
  imjcinit in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcinit.pas',
  imjcmainct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcmainct.pas',
  imjcmarker in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcmarker.pas',
  imjcmaster in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcmaster.pas',
  imjcomapi in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcomapi.pas',
  imjcparam in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcparam.pas',
  imjcphuff in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcphuff.pas',
  imjcprepct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcprepct.pas',
  imjcsample in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjcsample.pas',
  imjdapimin in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdapimin.pas',
  imjdapistd in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdapistd.pas',
  imjdcoefct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdcoefct.pas',
  imjdcolor in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdcolor.pas',
  imjdct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdct.pas',
  imjddctmgr in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjddctmgr.pas',
  imjdeferr in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdeferr.pas',
  imjdhuff in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdhuff.pas',
  imjdinput in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdinput.pas',
  imjdmainct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmainct.pas',
  imjdmarker in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmarker.pas',
  imjdmaster in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmaster.pas',
  imjdmerge in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdmerge.pas',
  imjdphuff in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdphuff.pas',
  imjdpostct in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdpostct.pas',
  imjdsample in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjdsample.pas',
  imjerror in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjerror.pas',
  imjfdctflt in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjfdctflt.pas',
  imjfdctfst in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjfdctfst.pas',
  imjfdctint in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjfdctint.pas',
  imjidctflt in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctflt.pas',
  imjidctfst in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctfst.pas',
  imjidctint in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctint.pas',
  imjidctred in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctred.pas',
  imjinclude in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjinclude.pas',
  imjmemmgr in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjmemmgr.pas',
  imjmemnobs in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjmemnobs.pas',
  imjmorecfg in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjmorecfg.pas',
  imjpeglib in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjpeglib.pas',
  imjquant1 in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjquant1.pas',
  imjquant2 in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjquant2.pas',
  imjutils in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjutils.pas',
  ImagingExtFileFormats in '..\..\src\vampyre_imaginglib\src\Extensions\ImagingExtFileFormats.pas',
  base64 in '..\..\src\compatibility\delphi-only\base64.pas',
  ctypes in '..\..\src\compatibility\delphi-only\ctypes.pas',
  custapp in '..\..\src\compatibility\delphi-only\custapp.pas',
  dom in '..\..\src\compatibility\delphi-only\dom.pas',
  FPHashCompatibility in '..\..\src\compatibility\delphi-only\FPHashCompatibility.pas',
  singleinstance in '..\..\src\compatibility\delphi-only\singleinstance.pas',
  uriparser in '..\..\src\compatibility\delphi-only\uriparser.pas',
  xmlread in '..\..\src\compatibility\delphi-only\xmlread.pas',
  xmlwrite in '..\..\src\compatibility\delphi-only\xmlwrite.pas',
  fpjson in '..\..\src\compatibility\delphi-only\fcl-json\fpjson.pas',
  fpjsonrtti in '..\..\src\compatibility\delphi-only\fcl-json\fpjsonrtti.pas',
  jsonparser in '..\..\src\compatibility\delphi-only\fcl-json\jsonparser.pas',
  jsonreader in '..\..\src\compatibility\delphi-only\fcl-json\jsonreader.pas',
  jsonscanner in '..\..\src\compatibility\delphi-only\fcl-json\jsonscanner.pas',
  CastlePasDblStrUtils in '..\..\src\scene\load\pasgltf\CastlePasDblStrUtils.pas',
  CastlePasGLTF in '..\..\src\scene\load\pasgltf\CastlePasGLTF.pas',
  CastlePasJSON in '..\..\src\scene\load\pasgltf\CastlePasJSON.pas',
  castlecontrols in '..\..\src\ui\castlecontrols.pas',
  castledialogviews in '..\..\src\ui\castledialogviews.pas',
  castleflasheffect in '..\..\src\ui\castleflasheffect.pas',
  castleinputs in '..\..\src\ui\castleinputs.pas',
  castleinternalcameragestures in '..\..\src\ui\castleinternalcameragestures.pas',
  castleinternalcontrolsimages in '..\..\src\ui\castleinternalcontrolsimages.pas',
  castleinternalinspector in '..\..\src\ui\castleinternalinspector.pas',
  castleinternalgamecontrollersexplicit in '..\..\src\ui\castleinternalgamecontrollersexplicit.pas',
  castleinternalgamecontrollerswindows in '..\..\src\ui\castleinternalgamecontrollerswindows.pas',
  castleinternalpk3dconnexion in '..\..\src\ui\castleinternalpk3dconnexion.pas',
  castleinternalsettings in '..\..\src\ui\castleinternalsettings.pas',
  castlegamecontrollers in '..\..\src\ui\castlegamecontrollers.pas',
  castlekeysmouse in '..\..\src\ui\castlekeysmouse.pas',
  castlenotifications in '..\..\src\ui\castlenotifications.pas',
  castleuicontrols in '..\..\src\ui\castleuicontrols.pas',
  castleglimages in '..\..\src\base_rendering\castleglimages.pas',
  castleglshaders in '..\..\src\base_rendering\castleglshaders.pas',
  castleglutils in '..\..\src\base_rendering\castleglutils.pas',
  castleglversion in '..\..\src\base_rendering\castleglversion.pas',
  castleinternalglutils in '..\..\src\base_rendering\castleinternalglutils.pas',
  castlerendercontext in '..\..\src\base_rendering\castlerendercontext.pas',
  castlerenderprimitives in '..\..\src\base_rendering\castlerenderprimitives.pas',
  castleinternalogg in '..\..\src\audio\ogg_vorbis\castleinternalogg.pas',
  castleinternalvorbiscodec in '..\..\src\audio\ogg_vorbis\castleinternalvorbiscodec.pas',
  castleinternalvorbisdecoder in '..\..\src\audio\ogg_vorbis\castleinternalvorbisdecoder.pas',
  castleinternalvorbisfile in '..\..\src\audio\ogg_vorbis\castleinternalvorbisfile.pas',
  castleimages in '..\..\src\images\castleimages.pas',
  castleinternalautogenerated in '..\..\src\images\castleinternalautogenerated.pas',
  castleinternalcompositeimage in '..\..\src\images\castleinternalcompositeimage.pas',
  castleinternaldatacompression in '..\..\src\images\castleinternaldatacompression.pas',
  castleinternalpng in '..\..\src\images\castleinternalpng.pas',
  castletextureimages in '..\..\src\images\castletextureimages.pas',
  castlevideos in '..\..\src\images\castlevideos.pas',
  castlefonts in '..\..\src\fonts\castlefonts.pas',
  castleinternalfreetype in '..\..\src\fonts\castleinternalfreetype.pas',
  castleinternalfreetypeh in '..\..\src\fonts\castleinternalfreetypeh.pas',
  castleinternalrichtext in '..\..\src\fonts\castleinternalrichtext.pas',
  castletexturefont_defaultui in '..\..\src\fonts\castletexturefont_defaultui.pas',
  castletexturefont_default3d_sans in '..\..\src\fonts\castletexturefont_default3d_sans.pas',
  castletexturefontdata in '..\..\src\fonts\castletexturefontdata.pas',
  castledebugtransform in '..\..\src\scene\castledebugtransform.pas',
  castleinternalarraysgenerator in '..\..\src\scene\castleinternalarraysgenerator.pas',
  castleinternalbackgroundrenderer in '..\..\src\scene\castleinternalbackgroundrenderer.pas',
  castleinternalbatchshapes in '..\..\src\scene\castleinternalbatchshapes.pas',
  castleinternalglcubemaps in '..\..\src\scene\castleinternalglcubemaps.pas',
  castleinternalmaterialproperties in '..\..\src\scene\castleinternalmaterialproperties.pas',
  castleinternalnodeinterpolator in '..\..\src\scene\castleinternalnodeinterpolator.pas',
  castleinternalnoise in '..\..\src\scene\castleinternalnoise.pas',
  castleinternalnormals in '..\..\src\scene\castleinternalnormals.pas',
  castleinternalprimitivematerial in '..\..\src\scene\castleinternalprimitivematerial.pas',
  castleinternalrenderer in '..\..\src\scene\castleinternalrenderer.pas',
  castleinternalshadowmaps in '..\..\src\scene\castleinternalshadowmaps.pas',
  castleinternalshapeoctree in '..\..\src\scene\castleinternalshapeoctree.pas',
  castleinternalspritesheet in '..\..\src\scene\castleinternalspritesheet.pas',
  castleinternaltriangleoctree in '..\..\src\scene\castleinternaltriangleoctree.pas',
  castleinternalx3dlexer in '..\..\src\scene\castleinternalx3dlexer.pas',
  castleinternalx3dscript in '..\..\src\scene\castleinternalx3dscript.pas',
  castlerendererinternallights in '..\..\src\scene\castlerendererinternallights.pas',
  castlerendererinternalshader in '..\..\src\scene\castlerendererinternalshader.pas',
  castlerendererinternaltextureenv in '..\..\src\scene\castlerendererinternaltextureenv.pas',
  castlescene in '..\..\src\scene\castlescene.pas',
  castlescenecore in '..\..\src\scene\castlescenecore.pas',
  castlesceneinternalblending in '..\..\src\scene\castlesceneinternalblending.pas',
  castleinternalocclusionculling in '..\..\src\scene\castleinternalocclusionculling.pas',
  castlesceneinternalshape in '..\..\src\scene\castlesceneinternalshape.pas',
  castlescreeneffects in '..\..\src\scene\castlescreeneffects.pas',
  castleshapeinternalrendershadowvolumes in '..\..\src\scene\castleshapeinternalrendershadowvolumes.pas',
  castleshapeinternalshadowvolumes in '..\..\src\scene\castleshapeinternalshadowvolumes.pas',
  castleshapes in '..\..\src\scene\castleshapes.pas',
  castleterrain in '..\..\src\scene\castleterrain.pas',
  castlethirdpersonnavigation in '..\..\src\scene\castlethirdpersonnavigation.pas',
  castletiledmap in '..\..\src\scene\castletiledmap.pas',
  castleviewport in '..\..\src\scene\castleviewport.pas',
  x3dcamerautils in '..\..\src\scene\x3dcamerautils.pas',
  x3dtime in '..\..\src\scene\x3dtime.pas',
  castlelivingbehaviors in '..\..\src\scene\castlelivingbehaviors.pas',
  kraft in '..\..\src\physics\kraft\kraft.pas',
  castleinternalkraftoverrides in '..\..\src\physics\kraft\castleinternalkraftoverrides.pas',
  ImagingXpm in '..\..\src\vampyre_imaginglib\src\Extensions\ImagingXpm.pas',
  ImagingPcx in '..\..\src\vampyre_imaginglib\src\Extensions\ImagingPcx.pas',
  ImagingPsd in '..\..\src\vampyre_imaginglib\src\Extensions\ImagingPsd.pas',
  castlescriptarrays in '..\..\src\castlescript\castlescriptarrays.pas',
  castlescriptimages in '..\..\src\castlescript\castlescriptimages.pas',
  castlescriptvectors in '..\..\src\castlescript\castlescriptvectors.pas',
  castleinternalshapesrenderer in '..\..\src\scene\castleinternalshapesrenderer.pas',
  castleinternalfilemonitor in '..\..\src\files\castleinternalfilemonitor.pas',
  castleinternalscreeneffects in '..\..\src\scene\castleinternalscreeneffects.pas',
  castleinternalnodesunsupported in '..\..\src\scene\x3d\castleinternalnodesunsupported.pas',
  imjidctasm in '..\..\src\vampyre_imaginglib\src\Source\JpegLib\imjidctasm.pas',
  castleinternalcontextbase in '..\..\src\base_rendering\castleinternalcontextbase.pas',
  castleinternalcontextegl in '..\..\src\base_rendering\castleinternalcontextegl.pas',
  castleinternalcontextglx in '..\..\src\base_rendering\castleinternalcontextglx.pas',
  castleinternalcontextwgl in '..\..\src\base_rendering\castleinternalcontextwgl.pas',
  castleinternalegl in '..\..\src\base_rendering\castleinternalegl.pas',
  castlegl in '..\..\src\base_rendering\dglopengl\castlegl.pas',
  castleinternalurlutils in '..\..\src\files\castleinternalurlutils.pas',
  castletransformmanipulate in '..\..\src\scene\castletransformmanipulate.pas',
  castleinternaltransformdata in '..\..\src\scene\castleinternaltransformdata.pas',
  castleinternalsteamapi in '..\..\src\services\steam\castleinternalsteamapi.pas',
  castlesteam in '..\..\src\services\steam\castlesteam.pas';

end.





