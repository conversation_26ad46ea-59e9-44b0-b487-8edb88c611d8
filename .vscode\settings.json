{"cSpell.words": ["-Mdel<PERSON>", "-Pwasm32", "-<PERSON><PERSON><PERSON><PERSON>", "-<PERSON><PERSON><PERSON>", "-Twasip1", "a2enmod", "a2ensite", "aarch", "adoc", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "allowtransparency", "alphanum", "Alya", "<PERSON><PERSON><PERSON>", "anims", "apidoc", "apptype", "ArchiCAD", "<PERSON><PERSON><PERSON>", "Artstation", "<PERSON><PERSON><PERSON><PERSON>", "ASideOfChidori", "astcenc", "astrophe", "AWSD", "backface", "baseinstalldir", "beforedestruction", "<PERSON><PERSON>", "bgfx", "Bielsko-Biała", "binutils", "blendswap", "boundingbox", "BSDF", "bugreport", "bump_demo_leaf_nobump", "Canneyt", "castleautogenerated", "castleconf", "castleengine", "castleinternaltools", "castleinternalwebgl_flat_api", "castlelivingbehaviors_moveattack", "castlewindow", "castlexxx", "cgeimg", "cgeref", "cgewp", "cheatsheet", "choco", "clearcoat", "cmdline", "codebases", "coderay", "<PERSON><PERSON><PERSON>", "collidable", "colorbox", "compileserver", "Compressonator", "compressonatorcli", "CPUI386", "CPUX86_64", "createconfig", "csSRGB", "cubemap", "cubemaps", "cubemaptexturing", "dcclinux", "devel", "devkit", "devlog", "devpassword", "devs", "DFSG", "distros", "dlclose", "dlopen", "dlsym", "downscaled", "DPROJ", "dscene", "dylib", "environmentaleffects", "<PERSON><PERSON>l", "Fensterbau", "filesystems", "Flah", "FMOD", "fmxlinux", "FPCDIR", "FPCTARGET", "FPCTARGETCPU", "fpcup", "fpcupdeluxe", "fpmake", "fppkg", "framebuffer", "framedesign", "freecad", "freedesktop", "freedesktops", "freetype", "gamedev", "gameembeddedimages", "gameinitialize", "gamepad", "gamepads", "gameviewmain", "gameviewmainmenu", "gameviewplay", "gamma_nogamma_helmet", "gconf", "geshi", "GLES", "glinformation", "globalunitdir", "glplotter", "GLSL", "gltf", "glxgears", "glxinfo", "<PERSON><PERSON><PERSON>", "grayscale", "Grayson", "Gzipped", "headshots", "heightmaps", "<PERSON><PERSON><PERSON>", "htdocs", "<PERSON>der<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ifdef", "ifndef", "indylaz", "inlines", "inlinesvg", "inout", "instantfpc", "invoke_noresult", "jetpack", "Jirtl", "<PERSON><PERSON><PERSON>", "kambi", "<PERSON><PERSON><PERSON><PERSON>", "kanim", "kdoll", "<PERSON><PERSON>", "keyup", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "killall", "Köln", "LAZARUSDIR", "lazbuild", "leaderboards", "<PERSON><PERSON>", "libaaa", "libcurl", "libegl", "libfreetype", "libgtk", "libnvtt", "libopenal", "libpath", "lib<PERSON><PERSON><PERSON>", "libqt", "libsteam", "libvorbisfile", "libxxx", "libz", "lldb", "longword", "Lu<PERSON><PERSON>", "Magick", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "menufile", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mipmaps", "Mixamo", "MJcMqfx6u98", "mod_userdir", "MonoBehaviour", "msvideo", "MSWINDOWS", "MSYS2", "Multibranch", "mycompany", "mycreature", "<PERSON><PERSON><PERSON>", "nav<PERSON>h", "noimage", "normalmap", "normalmaps", "notanimated", "notcollidable", "Npcs", "<PERSON><PERSON><PERSON>", "nvcompress", "nvim", "objfpc", "octree", "octrees", "openal", "opengameart", "opengl", "Optimus", "optirun", "pasdoc", "Pasj2s", "pasls", "<PERSON><PERSON>", "peiWjieZpbM", "<PERSON><PERSON>", "<PERSON><PERSON>", "Pickable", "pipefail", "platformer", "pluggable", "pointingdevicesensor", "prefs", "Programistów", "Pvrtc1_4bpp_RGBA", "<PERSON><PERSON><PERSON><PERSON>", "quickjs", "quicktime", "ragdolls", "raiseexception", "rayhunter", "ray<PERSON>", "Redist", "redistributable", "redistributables", "refcounting", "regionsequence", "resourcestring", "Revit", "RewriteCond", "RGBA", "RGBE", "rhan_shrine", "Rosseaux", "RTLD", "RTTI", "RTTT", "<PERSON><PERSON>", "savegames", "sdkmanager", "<PERSON>also", "<PERSON><PERSON><PERSON>", "setq", "sketchfab", "<PERSON><PERSON><PERSON>", "skybox", "skyboxes", "Slava", "slerp", "spatialized", "SPIR-V", "splatmap", "splatmaps", "steamworks", "subcomponents", "Subimage", "subimages", "swfl", "syslibroot", "Terragen", "Testcase", "testcases", "tetris", "<PERSON><PERSON>", "thumbnailer", "thumbnailers", "TIfcManifoldSolidBrep", "tileable", "tileset", "tilesets", "titlealt", "TLODNode", "todos", "toggable", "<PERSON>n", "tovrmlx", "TPCG", "TRGB", "Trung", "tweening", "unoptimal", "<PERSON><PERSON><PERSON>", "USDZ", "valgrind", "Vampyre", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewports", "Vorbis", "VRML", "Vulkan", "wasi", "WasiP1", "wasmtime", "WDkJ86-zfzw", "webgl", "WEBIDL", "webp", "webserver", "Wincompatible-sysroot", "WIREFFRAME", "Wireframe", "workarounded", "Writeln", "wwise", "Wyrd", "xcodeproj", "XLIB", "<PERSON><PERSON>'s", "ZEROBASEDSTRINGS", "Zlot"], "cSpell.enabled": true, "asciidoc.antora.enableAntoraSupport": false}