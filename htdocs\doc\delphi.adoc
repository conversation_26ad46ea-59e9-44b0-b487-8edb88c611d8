# Delphi
include::common.adoc[]
:description: Using Castle Game Engine with Delphi IDE and compiler.
:cge-social-share-image: delphi_code.png

== Introduction

We support link:https://www.embarcadero.com/products/Delphi[Delphi], a powerful commercial Pascal IDE and compiler.

Any Delphi tier, including free link:https://www.embarcadero.com/products/delphi/starter/free-download/[Delphi Community Edition], is fine.

// Mentioned lower:
//NOTE: Though note that Delphi _Community Edition_ versions don't have a working `dcc` (command-line) compiler, so compiling using F9 in CGE editor will not work. But you can simply open the project in Delphi and use F9 from _Delphi_.

We are an official _Embarcadero Technology Partner_. This means that engine developers have full access to the latest Delphi version, with all the Delphi platforms (including Android and iOS), for free. For testing CGE compatibility.

If you like Delphi compatibility, and want to see more of it (e.g. more platforms or Delphi-specific integration tools), please voice your needs and if you can -- link:https://www.patreon.com/castleengine[support us on Patreon]!

cgeimg::block[
  delphi_code.png|Castle Game Engine API in Delphi,
  delphi_control.png|Castle Game Engine and Delphi
]

== Configure Castle Game Engine to use Delphi

In _Castle Game Engine_ editor, open "_Preferences"_ dialog. In both the _"Code Editor"_ and _"Compilation"_ tabs set _"Delphi"_, if it was not auto-detected already.

That's it. CGE editor is now integrated with Delphi. You can test it:

* Press F9 to compile the project from CGE editor (using CGE build tool which will use Delphi command-line compiler under the hood).
+
NOTE: In case of Delphi _Community Edition_: It doesn't have command-line compiler, so you have to compile the project using F9 from the Delphi IDE.
* Double-click on Pascal files to open them in Delphi.
* Use _"Code -> Open Project in Code Editor"_ menu item to open project in Delphi.

## Presentations

video::epqLUe_HapM[youtube,width=800,height=450]

video::oA87iclrDZA[youtube,width=800,height=450]

video::6JfFxnZO4Jc[youtube,width=800,height=450]

## Platforms supported with Delphi

* We support now Windows (both 32-bit and 64-bit) and link:delphi_linux[Linux]. This means that you can compile and run your CGE applications on these platforms using Delphi. See link:delphi_linux[Linux] for Linux-specific setup notes.

* Other platforms will come, next one is most likely Android.

* You can use cgeref:TCastleWindow[], which is our standard way to create CGE window. Our _"New Project"_ templates as well as most examples use it.

* You can alternatively use cgeref:TCastleControl[] that allows to put CGE rendering on FMX (FireMonkey) or VCL form. link:control_on_form[See here for details].
+
To register the cgeref:TCastleControl[] component in Delphi IDE, follow link:delphi_packages[Delphi packages installation instructions].

## Delphi versions supported

* The engine is tested and supported on all Delphi versions >= 10.2.1. It was actively tested on 10.2, 10.3, 10.4 (including CE), and all Delphi 11 editions (including 11.3 CE).

* The exact Delphi version support from _Castle Game Engine_ depends on the target platform, since the support for non-Windows platforms isn't perfect in older Delphi versions:
+
--
Windows (32-bit and 64-bit):: Delphi >= 10.2.1
link:delphi_linux[Linux] (64-bit only):: Delphi >= 11.3
// (see https://github.com/castle-engine/castle-engine/issues/577[#577 for reasons])
Android, iOS, macOS (Upcoming):: Delphi >= 10.4 (see below for reasons; these requirements may change once we finalize these platforms)
--

////
|===
|Platform|Supported Delphi version

|Windows (32-bit and 64-bit)
|>= 10.2.1

|Linux
|>= 11.3

|Android, iOS (Upcoming)
|>= 10.4 (It may change once we finalize these platforms)
|===
////

////
* In the future, we could support Delphi versions >= XE 7.
+
Adjusting to Delphi XE 7 seems to require only a few local changes (see link:https://github.com/castle-engine/castle-engine/issues/482[XE7 test], link:https://github.com/castle-engine/castle-engine/issues/486[10.0.1 test], link:https://github.com/castle-engine/castle-engine/issues/488[10.1.2 test]).
+
We do not plan to support older Delphi versions. They are really old (link:https://en.wikipedia.org/wiki/History_of_Delphi_(software)[Delphi XE6 is from 2014]) and would require more work (see link:https://github.com/castle-engine/castle-engine/issues/481[XE4 test]). It doesn't seem wise to spend resources on maintaining (making and testing) compatibility with these ancient Delphi versions.
////

* We will not support Delphi versions older than 10.4 _for mobile (Android and iOS)_.
+
Reason: The older Delphi versions have crazy compatibility-breaking change to `String` handling on mobile: strings are 0-based (but only on mobile!, on desktop they remained 1-based) and _Embarcadero/Idera_ recommended way forward was to use `TStringHelper`, with all operations 0-based, and maybe treat strings as immutable.
+
This is documented now in link:https://docwiki.embarcadero.com/RADStudio/Alexandria/en/Zero-based_strings_(Delphi)[Zero-based strings (Delphi)]. In the past page link:https://docwiki.embarcadero.com/RADStudio/XE7/en/Migrating_Delphi_Code_to_Mobile_from_Desktop#Use_0-Based_Strings[Migrating Delphi Code to Mobile from Desktop (for Delphi XE7)] had a longer section _"Use 0-Based Strings"_.
+
Adjusting to this would mean rewriting all `String` handling code in the engine to use new `TStringHelper`. And it would be quite risky task &mdash; as the global `String` routines remained available for mobile, but they were nearly useless for cross-platform code, as they would use 1-based on desktop and 0-based on mobile. Thus causing hard-to-find bugs, as the same code would _compile_ everywhere, but would work _differently_ between desktop and mobile.
+
We're happy that Embarcadero backed off from this weird decision in later Delphi versions. See link:https://docwiki.embarcadero.com/RADStudio/Sydney/en/Zero-based_strings_(Delphi)[Delphi 10.4 (Sydney): Zero-based strings (Delphi)]. In particular it says _"In general terms, string indexing is now uniform across platforms and uses 1-based index model."_ and _"Default"_ is _"`{$ZEROBASEDSTRINGS OFF}` for Delphi desktop and mobile compilers"_.

[#cpp_builder]
## {Cpp} Builder

The engine is fully compatible with _{Cpp} Builder_. The complete engine class API is available from {Cpp} in a natural way. You can use cgeref:TCastleWindow[] or cgeref:TCastleControl[] from {Cpp}, just like from Pascal. See https://castle-engine.io/wp/2023/12/01/full-compatibility-with-c-builder/[the details in this article].

There are 2 examples in CGE specifically for _{Cpp} Builder_, using engine API from {Cpp}:

- https://github.com/castle-engine/castle-engine/tree/master/examples/delphi/cpp_builder/fmx[examples/delphi/cpp_builder/fmx] shows using cgeref:TCastleControl[] inside FMX form.

- https://github.com/castle-engine/castle-engine/tree/master/examples/delphi/cpp_builder/window[examples/delphi/cpp_builder/window] shows using cgeref:TCastleWindow[], with link:views[], loading a design, operating on cgeref:TCastleScene[] and more. It faithfully recreates in {Cpp} the CGE Pascal example from the link:https://github.com/castle-engine/conference-itdevcon-2023/[ITDevCon 2023 conference].

NOTE: For _{Cpp} Builder_ you need to _Build_ the link:delphi_packages[packages] **for all your target platforms**, which means: both Win32 and Win64. This is in contrast to usage from Pascal, where it is enough to only _Install_ them for Win32 (Delphi IDE architecture).

The _{Cpp} Builder_ compatibility is available for _{Cpp} Builder_ versions 11.3 and up.

### Redistributing C++ Builder applications by default requires redistribution of Delphi run-time BPLs too

_{Cpp} Builder_ applications by default require redistribution of Delphi run-time libraries (BPL files) too. This is unrelated to CGE. So you cannot just give your users the resulting EXE file, you need to also provide all the BPL (run-time packages) it uses. If you use CGE, then in addition to RTL and FMX, you will also need to redistribute CGE run-time packages (BPL files).

If you forget to distribute the BPL files along with your EXE, users will see an error message like this:

cgeimg::block[
  cpp_builder_bpl_missing.png|Error message when BPL files are missing
]

If you want to avoid it, and have everything contained in one big EXE file, follow this answer; https://blogs.embarcadero.com/how-do-i-create-a-stand-alone-application-in-c-builder/[How do I create a stand-alone application in C++Builder] for a solution. In short (as of RAD Studio 12.0) uncheck in project options

1. "_Building -> {Cpp} Linker -> Link with Dynamic RTL_" and

2. "_Packages > Runtime Packages -> Link with runtime packages_".

Applications are then compiled into a larger EXE, that is "stand-alone" and does not require BPL files to be distributed.

We have tested the above solution, it works for simple FMX applications and CGE applications using cgeref:TCastleControl[] like https://github.com/castle-engine/castle-engine/tree/master/examples/delphi/cpp_builder/fmx[examples/delphi/cpp_builder/fmx]. Below is a screenshot, as you can see in the background we only transferred the EXE file to another computer and the `data` subdirectory.

cgeimg::block[
  cpp_builder_no_bpl_required.png|No BPL files required
]

WARNING: Unfortunately https://github.com/castle-engine/castle-engine/tree/master/examples/delphi/cpp_builder/window[examples/delphi/cpp_builder/window] with these settings (no run-time packages) makes _{Cpp} Builder_ compiler crash with _Access Violation_. We haven't tested whether this problem affects all _{Cpp} Builder_ applications using cgeref:TCastleWindow[], the reason behind it is also unknown -- cgeref:TCastleWindow[] isn't anything special, it just uses WinAPI (on Windows) under the hood to create a window with OpenGL context.
