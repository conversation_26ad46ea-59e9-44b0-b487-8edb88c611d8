.\Win64\Debug\File1.o: \
  C:\cygwin64\home\michalis\sources\castle-engine\castle-engine\examples\delphi\cpp_builder\window\CastleCppWindowPCH1.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\tchar.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_stddef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_defs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_null.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_str.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\locale.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_loc.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\mem.h \
  File1.cpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\iostream \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\istream \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\ostream \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\ios \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xlocnum \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\limits \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\ymath.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\yvals.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xkeycheck.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\stdarg.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cfloat \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\float.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\climits \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\limits.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_lim.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cmath \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\math.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xtgmath.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xtr1common \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cstddef \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\stddef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cstdlib \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\stdlib.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\search.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\errno.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cwchar \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\wchar.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xstddef \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\initializer_list \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cstdio \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\stdio.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\_nfile.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\streambuf \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xiosbase \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xlocale \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cstring \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\string.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\stdexcept \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\exception \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xxexception \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xstring \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xstring_view \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xmemory0 \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cstdint \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\stdint.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\new \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xutility \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\utility \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\iosfwd \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\type_traits \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\execution \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xiter \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xscan \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xparallel.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xthreadpool.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\atomic \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xatomic.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xatomic0.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xxatomic \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xmemory1 \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\typeinfo \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\typeinfo.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xlocinfo \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cctype \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\ctype.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\mbctype.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\clocale \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\ctime \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\time.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xlocinfo.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\system_error \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cerrno \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleWindow.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysmac.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\windows.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winapifamily.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winpackagefamily.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\sdkddkver.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\excpt.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\windef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\minwindef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\specstrings.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\sal.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\concurrencysal.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\specstrings_strict.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\driverspecs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/sdv_driverspecs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\no_sal2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winnt.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\kernelspecs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\basetsd.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\guiddef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/pshpack4.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/poppack.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\pshpack4.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\poppack.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/pshpack2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\pshpack2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/pshpack8.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/pshpack1.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\apiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ktmtypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\apisetcconv.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\minwinbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\apiquery2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\processenv.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\fileapifromapp.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\fileapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\debugapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\utilapiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\handleapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\errhandlingapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\fibersapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\namedpipeapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\profileapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\heapapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ioapiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\synchapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\interlockedapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\processthreadsapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\sysinfoapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\memoryapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\enclaveapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\threadpoollegacyapiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\threadpoolapiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\jobapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\jobapi2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wow64apiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\libloaderapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\securitybaseapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\namespaceapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\systemtopologyapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\processtopologyapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\securityappcontainer.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\realtimeapiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winerror.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\timezoneapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wingdi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\pshpack1.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winuser.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\tvout.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winnls.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\datetimeapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\stringapiset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wincon.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wincontypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\consoleapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\consoleapi2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\consoleapi3.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winver.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\verrsrc.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winreg.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\reason.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winnetwk.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wnnc.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\cderr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\dde.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ddeml.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\dlgs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\lzexpand.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mmsystem.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mmsyscom.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mciapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mmiscapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mmiscapi2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\playsoundapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mmeapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\timeapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\joystickapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\nb30.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpc.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\pshpack8.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcdce.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcdcep.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcnsi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcnterr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcasync.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shellapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winperf.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winsock2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ws2def.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\inaddr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\qos.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wincrypt.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\bcrypt.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ncrypt.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\dpapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winefs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winscard.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wtypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/rpc.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/rpcndr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcnsip.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcsal.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/wtypesbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/guiddef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winioctl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/winsmcrd.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winspool.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\prsht.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ole2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\objbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\rpcndr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\combaseapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wtypesbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\unknwnbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/windows.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/ole2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\objidlbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/unknwnbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\cguid.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\coml2api.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\objidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/unknwn.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/wtypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\propidlbase.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/objidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/oaidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\urlmon.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/oleidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/servprov.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/msxml.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\propidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\oleauto.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\oaidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\oleidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\commdlg.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\stralign.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\winsvc.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\mcx.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\imm.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ime_cmodes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\delayimp.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\basetyps.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\memory \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xmemory \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysclass.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\ustring.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Internal.StrHlpr.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\SysInit.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\string_view \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\string \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\dstring.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\wstring.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\syshash.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\functional \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\tuple \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xfunctional \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\xfunctional2 \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\systobj.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\systdate.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\syscurr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\syscomp.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysset.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\systvar.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysdyn.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\iterator \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\dinkumware64\cassert \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\crtl\assert.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysopen.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysvari.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\unknwn.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\autoargs.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\sysiterator.h \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalContextWgl.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.Windows.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Types.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\SystemTypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ocidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/urlmon.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.UITypes.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wintrust.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wtsapi32.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.SysUtils.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.SysConst.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Classes.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.Messages.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Generics.Defaults.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.TypInfo.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Variants.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Generics.Collections.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Rtti.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.SyncObjs.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.RTLConsts.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.TimeSpan.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\SystemRtti.h \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleVectors.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Math.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleUtils.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.ShlObj.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.ActiveX.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\olectl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\docobj.H \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/ocidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\comcat.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\dispex.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\objsafe.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\propvarutil.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\propapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shtypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shlwapi.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.CommCtrl.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\CommCtrl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\dpa_dsa.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.ShellAPI.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.Winsock2.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.Qos.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.IpExport.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ipexport.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\in6addr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.RegStr.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\regstr.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.WinInet.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\wininet.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.UrlMon.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.ObjectArray.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\ObjectArray.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.PropSys.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.StructuredQueryCondition.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\structuredquerycondition.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/propidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\propsys.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/shtypes.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/StructuredQueryCondition.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\propkeydef.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.MSXMLIntf.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\MsXml2.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shlguid.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\isguids.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\exdisp.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/docobj.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shldisp.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\knownfolders.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shlobj.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shobjidl.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/comcat.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/prsht.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/propsys.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/ObjectArray.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk/shobjidl_core.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\sherrors.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shlobj_core.h \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\sdk\shobjidl_core.h \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleVectorsInternalSingle.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleVectorsInternalDouble.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleClassUtils.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Contnrs.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleStringUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleRenderOptions.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleColors.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalContextBase.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\System.Character.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.Imm.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Winapi.CommDlg.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CustApp.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\singleinstance.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\ctypes.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleGL.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleDynLib.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleRectangles.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleGLUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleImages.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalPng.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalZLib.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleFileFilters.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleProjection.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleGLImages.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleTimeUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleTextureImages.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalCompositeImage.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleVideos.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleGLShaders.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalGLUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalFileMonitor.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleKeysMouse.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleXMLConfig.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\DOM.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Xml.XMLDoc.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Xml.xmldom.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Xml.XMLConst.hpp \
  c:\program\ files\ (x86)\embarcadero\studio\23.0\include\windows\rtl\Xml.XMLIntf.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleXMLCfgInternal.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\XMLRead.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\XMLWrite.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleXmlUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleDownload.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleUriUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleFindFiles.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleFilesUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleUIControls.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleFonts.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleTextureFontData.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleUnicode.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalFreeType.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalFreeTypeH.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleApplicationProperties.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalPk3DConnexion.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleGameControllers.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleRenderContext.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleComponentSerialize.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\fpjson.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\FPHashCompatibility.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\fpjsonrtti.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\jsonparser.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\jsonscanner.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\jsonreader.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalRttiUtils.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleMessaging.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleParameters.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleSoundEngine.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleSoundBase.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalSoundFile.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalVorbisFile.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalVorbisCodec.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalOgg.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleInternalAbstractSoundBackend.hpp \
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win64\CastleLog.hpp \
  code\GameInitialize.h
