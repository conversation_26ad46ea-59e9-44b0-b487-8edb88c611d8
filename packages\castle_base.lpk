<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <Package Version="5">
    <Name Value="castle_base"/>
    <Type Value="RunAndDesignTime"/>
    <AddToProjectUsesSection Value="True"/>
    <Author Value="<PERSON><PERSON><PERSON>"/>
    <CompilerOptions>
      <Version Value="11"/>
      <SearchPaths>
        <IncludeFiles Value="../src/common_includes;../src/base;../src/base/unix;../src/base/windows;../src/audio;../src/castlescript;../src/transform;../src/scene/glsl/generated-pascal;../src/scene;../src/scene/x3d;../src/scene/load;../src/scene/load/spine;../src/scene/load/md3;../src/scene/load/collada;../src/scene/load/pasgltf;../src/base_rendering;../src/physics/kraft;../src/base_rendering/dglopengl;../src/base_rendering/glsl/generated-pascal;../src/images;../src/scene/x3d/auto_generated_node_helpers;../src/ui;../src/deprecated_units;../src/files;../src/files/tools;../src/fonts;../src/transform/auto_generated_persistent_vectors;../src/vampyre_imaginglib/src/Source;../src/vampyre_imaginglib/src/Source/JpegLib;../src/vampyre_imaginglib/src/Source/ZLib;../src/scene/transform_manipulate_data/generated-pascal;../src/scene/load/ifc;../src/services/steam;../src/ui/designs;../src/services"/>
        <OtherUnitFiles Value="../src/base;../src/transform;../src/images;../src/fonts;../src/audio;../src/files;../src/files/tools;../src/castlescript;../src/ui;../src/ui/windows;../src/services;../src/base_rendering;../src/base_rendering/dglopengl;../src/physics/kraft;../src/scene;../src/scene/x3d;../src/scene/load;../src/scene/load/spine;../src/scene/load/md3;../src/scene/load/collada;../src/scene/load/pasgltf;../src/audio/openal;../src/audio/ogg_vorbis;../src/audio/fmod;../src/deprecated_units;../src/vampyre_imaginglib/src/Source;../src/vampyre_imaginglib/src/Source/JpegLib;../src/vampyre_imaginglib/src/Source/ZLib;../src/vampyre_imaginglib/src/Extensions;../src/vampyre_imaginglib/src/Extensions/LibTiff;../src/scene/load/ifc;../src/services/steam"/>
        <UnitOutputDirectory Value="lib/castle_base/$(TargetCPU)-$(TargetOS)"/>
      </SearchPaths>
      <Conditionals Value="// example for adding linker options on macOS
//if TargetOS=&apos;darwin&apos; then
//  LinkerOptions := &apos; -framework OpenGL&apos;;

// example for adding a unit and include path on Windows
//if SrcOS=&apos;win&apos; then begin
//  UnitPath += &apos;;win&apos;;
//  IncPath += &apos;;win&apos;;
//end;

// See http://wiki.freepascal.org/Macros_and_Conditionals"/>
      <Parsing>
        <SyntaxOptions>
          <CStyleMacros Value="True"/>
        </SyntaxOptions>
      </Parsing>
      <CodeGeneration>
        <Checks>
          <IOChecks Value="True"/>
        </Checks>
        <Optimizations>
          <OptimizationLevel Value="2"/>
        </Optimizations>
      </CodeGeneration>
      <Other>
        <Verbosity>
          <ShowHints Value="False"/>
        </Verbosity>
        <CompilerMessages>
          <IgnoredMessages idx5063="True" idx4046="True"/>
        </CompilerMessages>
        <ConfigFile>
          <CustomConfigFile Value="True"/>
          <ConfigFilePath Value="../castle-fpc-messages.cfg"/>
        </ConfigFile>
        <CustomOptions Value="-dCASTLE_ENGINE_LAZARUS_PACKAGE"/>
      </Other>
    </CompilerOptions>
    <Description Value="Castle Game Engine is an open-source 3D and 2D game engine. We support many game model formats (glTF, X3D, Spine...), we are cross-platform (desktop, mobile, console), we have an optimized renderer with many cool graphic effects (physically-based rendering, shadows, mirrors, bump mapping, gamma correction...). See https://castle-engine.io/features.php for a complete list of features.

This package, castle_base.lpk, contains the core engine units. It is not dependent on CastleWindow or Lazarus LCL.

You can install this package in Lazarus, it&apos;s required by all other programs and packages that are part of Castle Game Engine."/>
    <License Value="GNU LGPL with static linking exception >= 2.
This is the same license as used by Lazarus LCL and FPC RTL.
See https://castle-engine.io/license for details.
"/>
    <Version Major="6" Minor="93" Release="1"/>
    <Files Count="1151">
      <Item1>
        <Filename Value="../src/audio/castleinternalabstractsoundbackend.pas"/>
        <UnitName Value="CastleInternalAbstractSoundBackend"/>
      </Item1>
      <Item2>
        <Filename Value="../src/audio/castleinternalsoundfile.pas"/>
        <UnitName Value="CastleInternalSoundFile"/>
      </Item2>
      <Item3>
        <Filename Value="../src/audio/castleinternalsoxsoundbackend.pas"/>
        <UnitName Value="CastleInternalSoxSoundBackend"/>
      </Item3>
      <Item4>
        <Filename Value="../src/audio/castlesoundbase.pas"/>
        <UnitName Value="CastleSoundBase"/>
      </Item4>
      <Item5>
        <Filename Value="../src/audio/castlesoundengine.pas"/>
        <UnitName Value="CastleSoundEngine"/>
      </Item5>
      <Item6>
        <Filename Value="../src/audio/castlesoundengine_allocator.inc"/>
        <Type Value="Include"/>
      </Item6>
      <Item7>
        <Filename Value="../src/audio/castlesoundengine_engine.inc"/>
        <Type Value="Include"/>
      </Item7>
      <Item8>
        <Filename Value="../src/audio/castlesoundengine_initial_types.inc"/>
        <Type Value="Include"/>
      </Item8>
      <Item9>
        <Filename Value="../src/audio/castlesoundengine_internalsoundbuffer.inc"/>
        <Type Value="Include"/>
      </Item9>
      <Item10>
        <Filename Value="../src/audio/castlesoundengine_internalsoundsource.inc"/>
        <Type Value="Include"/>
      </Item10>
      <Item11>
        <Filename Value="../src/audio/castlesoundengine_loopingchannel.inc"/>
        <Type Value="Include"/>
      </Item11>
      <Item12>
        <Filename Value="../src/audio/castlesoundengine_miscellaneous.inc"/>
        <Type Value="Include"/>
      </Item12>
      <Item13>
        <Filename Value="../src/audio/castlesoundengine_playingsound.inc"/>
        <Type Value="Include"/>
      </Item13>
      <Item14>
        <Filename Value="../src/audio/castlesoundengine_playsoundparameters.inc"/>
        <Type Value="Include"/>
      </Item14>
      <Item15>
        <Filename Value="../src/audio/castlesoundengine_repoengine.inc"/>
        <Type Value="Include"/>
      </Item15>
      <Item16>
        <Filename Value="../src/audio/castlesoundengine_sound.inc"/>
        <Type Value="Include"/>
      </Item16>
      <Item17>
        <Filename Value="../src/audio/fmod/castlefmodsoundbackend.pas"/>
        <UnitName Value="CastleFMODSoundBackend"/>
      </Item17>
      <Item18>
        <Filename Value="../src/audio/fmod/castleinternalfmod.pas"/>
        <UnitName Value="CastleInternalFMOD"/>
      </Item18>
      <Item19>
        <Filename Value="../src/audio/fmod/castleinternalfmod_dynamic.inc"/>
        <Type Value="Include"/>
      </Item19>
      <Item20>
        <Filename Value="../src/audio/fmod/castleinternalfmod_static.inc"/>
        <Type Value="Include"/>
      </Item20>
      <Item21>
        <Filename Value="../src/audio/ogg_vorbis/castleinternalogg.pas"/>
        <UnitName Value="CastleInternalOgg"/>
      </Item21>
      <Item22>
        <Filename Value="../src/audio/ogg_vorbis/castleinternalvorbiscodec.pas"/>
        <UnitName Value="CastleInternalVorbisCodec"/>
      </Item22>
      <Item23>
        <Filename Value="../src/audio/ogg_vorbis/castleinternalvorbisdecoder.pas"/>
        <UnitName Value="CastleInternalVorbisDecoder"/>
      </Item23>
      <Item24>
        <Filename Value="../src/audio/ogg_vorbis/castleinternalvorbisfile.pas"/>
        <UnitName Value="CastleInternalVorbisFile"/>
      </Item24>
      <Item25>
        <Filename Value="../src/audio/openal/castleinternalalutils.pas"/>
        <UnitName Value="CastleInternalALUtils"/>
      </Item25>
      <Item26>
        <Filename Value="../src/audio/openal/castleinternalefx.pas"/>
        <UnitName Value="CastleInternalEFX"/>
      </Item26>
      <Item27>
        <Filename Value="../src/audio/openal/castleinternalefx_api.inc"/>
        <Type Value="Include"/>
      </Item27>
      <Item28>
        <Filename Value="../src/audio/openal/castleinternalefx_api_creative.inc"/>
        <Type Value="Include"/>
      </Item28>
      <Item29>
        <Filename Value="../src/audio/openal/castleinternalopenal.pas"/>
        <UnitName Value="CastleInternalOpenAL"/>
      </Item29>
      <Item30>
        <Filename Value="../src/audio/openal/castleinternalopenal_al.inc"/>
        <Type Value="Include"/>
      </Item30>
      <Item31>
        <Filename Value="../src/audio/openal/castleinternalopenal_alc.inc"/>
        <Type Value="Include"/>
      </Item31>
      <Item32>
        <Filename Value="../src/audio/openal/castleinternalopenal_alctypes.inc"/>
        <Type Value="Include"/>
      </Item32>
      <Item33>
        <Filename Value="../src/audio/openal/castleinternalopenal_altypes.inc"/>
        <Type Value="Include"/>
      </Item33>
      <Item34>
        <Filename Value="../src/audio/openal/castleinternalopenal_alut.inc"/>
        <Type Value="Include"/>
      </Item34>
      <Item35>
        <Filename Value="../src/audio/openal/castleopenalsoundbackend.pas"/>
        <UnitName Value="CastleOpenALSoundBackend"/>
      </Item35>
      <Item36>
        <Filename Value="../src/base/auto_generated_persistent_vectors/tcastlerenderoptions_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item36>
      <Item37>
        <Filename Value="../src/base/castleapplicationproperties.pas"/>
        <UnitName Value="CastleApplicationProperties"/>
      </Item37>
      <Item38>
        <Filename Value="../src/base/castleclassutils.pas"/>
        <UnitName Value="CastleClassUtils"/>
      </Item38>
      <Item39>
        <Filename Value="../src/base/castlecolors.pas"/>
        <UnitName Value="CastleColors"/>
      </Item39>
      <Item40>
        <Filename Value="../src/base/castlecolors_persistent.inc"/>
        <Type Value="Include"/>
      </Item40>
      <Item41>
        <Filename Value="../src/base/castledynlib.pas"/>
        <UnitName Value="CastleDynLib"/>
      </Item41>
      <Item42>
        <Filename Value="../src/base/castleinternalclassutils.pas"/>
        <UnitName Value="CastleInternalClassUtils"/>
      </Item42>
      <Item43>
        <Filename Value="../src/base/castleinternalgzio.pas"/>
        <UnitName Value="CastleInternalGzio"/>
      </Item43>
      <Item44>
        <Filename Value="../src/base/castleinternalrttiutils.pas"/>
        <UnitName Value="CastleInternalRttiUtils"/>
      </Item44>
      <Item45>
        <Filename Value="../src/base/castleinternalzlib.pas"/>
        <UnitName Value="CastleInternalZLib"/>
      </Item45>
      <Item46>
        <Filename Value="../src/base/castleinternalzstream.pas"/>
        <UnitName Value="CastleInternalZStream"/>
      </Item46>
      <Item47>
        <Filename Value="../src/base/castlelog.pas"/>
        <UnitName Value="CastleLog"/>
      </Item47>
      <Item48>
        <Filename Value="../src/base/castlemessaging.pas"/>
        <UnitName Value="CastleMessaging"/>
      </Item48>
      <Item49>
        <Filename Value="../src/base/castleparameters.pas"/>
        <UnitName Value="CastleParameters"/>
      </Item49>
      <Item50>
        <Filename Value="../src/base/castleprojection.pas"/>
        <UnitName Value="CastleProjection"/>
      </Item50>
      <Item51>
        <Filename Value="../src/base/castlequaternions.pas"/>
        <UnitName Value="CastleQuaternions"/>
      </Item51>
      <Item52>
        <Filename Value="../src/base/castlerectangles.pas"/>
        <UnitName Value="CastleRectangles"/>
      </Item52>
      <Item53>
        <Filename Value="../src/base/castlerectangles_persistent.inc"/>
        <Type Value="Include"/>
      </Item53>
      <Item54>
        <Filename Value="../src/base/castlerenderoptions.pas"/>
        <UnitName Value="CastleRenderOptions"/>
      </Item54>
      <Item55>
        <Filename Value="../src/base/castlerenderoptions_globals.inc"/>
        <Type Value="Include"/>
      </Item55>
      <Item56>
        <Filename Value="../src/base/castlerenderoptions_renderoptions.inc"/>
        <Type Value="Include"/>
      </Item56>
      <Item57>
        <Filename Value="../src/base/castlestreamutils.pas"/>
        <UnitName Value="CastleStreamUtils"/>
      </Item57>
      <Item58>
        <Filename Value="../src/base/castlestringutils.pas"/>
        <UnitName Value="CastleStringUtils"/>
      </Item58>
      <Item59>
        <Filename Value="../src/base/castlesystemlanguage.pas"/>
        <UnitName Value="CastleSystemLanguage"/>
      </Item59>
      <Item60>
        <Filename Value="../src/base/castletimeutils.pas"/>
        <UnitName Value="CastleTimeUtils"/>
      </Item60>
      <Item61>
        <Filename Value="../src/base/castletimeutils_frameprofiler.inc"/>
        <Type Value="Include"/>
      </Item61>
      <Item62>
        <Filename Value="../src/base/castletimeutils_framespersecond.inc"/>
        <Type Value="Include"/>
      </Item62>
      <Item63>
        <Filename Value="../src/base/castletimeutils_gettickcount64.inc"/>
        <Type Value="Include"/>
      </Item63>
      <Item64>
        <Filename Value="../src/base/castletimeutils_miscellaneous.inc"/>
        <Type Value="Include"/>
      </Item64>
      <Item65>
        <Filename Value="../src/base/castletimeutils_now.inc"/>
        <Type Value="Include"/>
      </Item65>
      <Item66>
        <Filename Value="../src/base/castletimeutils_processtimer.inc"/>
        <Type Value="Include"/>
      </Item66>
      <Item67>
        <Filename Value="../src/base/castletimeutils_profiler.inc"/>
        <Type Value="Include"/>
      </Item67>
      <Item68>
        <Filename Value="../src/base/castletimeutils_timer.inc"/>
        <Type Value="Include"/>
      </Item68>
      <Item69>
        <Filename Value="../src/base/castleunicode.pas"/>
        <UnitName Value="CastleUnicode"/>
      </Item69>
      <Item70>
        <Filename Value="../src/base/castleutils.pas"/>
        <UnitName Value="CastleUtils"/>
      </Item70>
      <Item71>
        <Filename Value="../src/base/castleutils_delphi_compatibility.inc"/>
        <Type Value="Include"/>
      </Item71>
      <Item72>
        <Filename Value="../src/base/castleutils_filenames.inc"/>
        <Type Value="Include"/>
      </Item72>
      <Item73>
        <Filename Value="../src/base/castleutils_math.inc"/>
        <Type Value="Include"/>
      </Item73>
      <Item74>
        <Filename Value="../src/base/castleutils_miscella.inc"/>
        <Type Value="Include"/>
      </Item74>
      <Item75>
        <Filename Value="../src/base/castleutils_platform.inc"/>
        <Type Value="Include"/>
      </Item75>
      <Item76>
        <Filename Value="../src/base/castleutils_pointers.inc"/>
        <Type Value="Include"/>
      </Item76>
      <Item77>
        <Filename Value="../src/base/castleutils_primitive_lists.inc"/>
        <Type Value="Include"/>
      </Item77>
      <Item78>
        <Filename Value="../src/base/castleutils_program_exit.inc"/>
        <Type Value="Include"/>
      </Item78>
      <Item79>
        <Filename Value="../src/base/castleutils_read_write.inc"/>
        <Type Value="Include"/>
      </Item79>
      <Item80>
        <Filename Value="../src/base/castleutils_struct_list.inc"/>
        <Type Value="Include"/>
      </Item80>
      <Item81>
        <Filename Value="../src/base/castleutils_types.inc"/>
        <Type Value="Include"/>
      </Item81>
      <Item82>
        <Filename Value="../src/base/castlevectors.pas"/>
        <UnitName Value="CastleVectors"/>
      </Item82>
      <Item83>
        <Filename Value="../src/base/castlevectors_border.inc"/>
        <Type Value="Include"/>
      </Item83>
      <Item84>
        <Filename Value="../src/base/castlevectors_byte.inc"/>
        <Type Value="Include"/>
      </Item84>
      <Item85>
        <Filename Value="../src/base/castlevectors_cardinal.inc"/>
        <Type Value="Include"/>
      </Item85>
      <Item86>
        <Filename Value="../src/base/castlevectors_compatibility_deprecated.inc"/>
        <Type Value="Include"/>
      </Item86>
      <Item87>
        <Filename Value="../src/base/castlevectors_double.inc"/>
        <Type Value="Include"/>
      </Item87>
      <Item88>
        <Filename Value="../src/base/castlevectors_float.inc"/>
        <Type Value="Include"/>
      </Item88>
      <Item89>
        <Filename Value="../src/base/castlevectors_generic_float_record.inc"/>
        <Type Value="Include"/>
      </Item89>
      <Item90>
        <Filename Value="../src/base/castlevectors_integer.inc"/>
        <Type Value="Include"/>
      </Item90>
      <Item91>
        <Filename Value="../src/base/castlevectors_lists.inc"/>
        <Type Value="Include"/>
      </Item91>
      <Item92>
        <Filename Value="../src/base/castlevectors_lists_double.inc"/>
        <Type Value="Include"/>
      </Item92>
      <Item93>
        <Filename Value="../src/base/castlevectors_matrix_decompose.inc"/>
        <Type Value="Include"/>
      </Item93>
      <Item94>
        <Filename Value="../src/base/castlevectors_miscellaneous.inc"/>
        <Type Value="Include"/>
      </Item94>
      <Item95>
        <Filename Value="../src/base/castlevectors_persistent.inc"/>
        <Type Value="Include"/>
      </Item95>
      <Item96>
        <Filename Value="../src/base/castlevectors_single.inc"/>
        <Type Value="Include"/>
      </Item96>
      <Item97>
        <Filename Value="../src/base/castlevectors_smallint.inc"/>
        <Type Value="Include"/>
      </Item97>
      <Item98>
        <Filename Value="../src/base/castlevectors_transformation.inc"/>
        <Type Value="Include"/>
      </Item98>
      <Item99>
        <Filename Value="../src/base/castlevectorsinternaldouble.pas"/>
        <UnitName Value="CastleVectorsInternalDouble"/>
      </Item99>
      <Item100>
        <Filename Value="../src/base/castlevectorsinternalsingle.pas"/>
        <UnitName Value="CastleVectorsInternalSingle"/>
      </Item100>
      <Item101>
        <Filename Value="../src/base/castleversion.inc"/>
        <Type Value="Include"/>
      </Item101>
      <Item102>
        <Filename Value="../src/base/gl_texture_compression_constants.inc"/>
        <Type Value="Include"/>
      </Item102>
      <Item103>
        <Filename Value="../src/base/unix/castleutils_os_specific_unix.inc"/>
        <Type Value="Include"/>
      </Item103>
      <Item104>
        <Filename Value="../src/base/wasi/castleutils_os_specific_wasi.inc"/>
        <Type Value="Include"/>
      </Item104>
      <Item105>
        <Filename Value="../src/base/windows/castleutils_os_specific_windows.inc"/>
        <Type Value="Include"/>
      </Item105>
      <Item106>
        <Filename Value="../src/base_rendering/auto_generated_persistent_vectors/tcastleimagepersistent_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item106>
      <Item107>
        <Filename Value="../src/base_rendering/castlegles.pas"/>
        <UnitName Value="CastleGLES"/>
      </Item107>
      <Item108>
        <Filename Value="../src/base_rendering/castleglimages.pas"/>
        <UnitName Value="CastleGLImages"/>
      </Item108>
      <Item109>
        <Filename Value="../src/base_rendering/castleglimages_drawableimage.inc"/>
        <Type Value="Include"/>
      </Item109>
      <Item110>
        <Filename Value="../src/base_rendering/castleglimages_drawableimagecache.inc"/>
        <Type Value="Include"/>
      </Item110>
      <Item111>
        <Filename Value="../src/base_rendering/castleglimages_filter.inc"/>
        <Type Value="Include"/>
      </Item111>
      <Item112>
        <Filename Value="../src/base_rendering/castleglimages_load_2d.inc"/>
        <Type Value="Include"/>
      </Item112>
      <Item113>
        <Filename Value="../src/base_rendering/castleglimages_load_3d.inc"/>
        <Type Value="Include"/>
      </Item113>
      <Item114>
        <Filename Value="../src/base_rendering/castleglimages_load_cubemap.inc"/>
        <Type Value="Include"/>
      </Item114>
      <Item115>
        <Filename Value="../src/base_rendering/castleglimages_miscellaneous.inc"/>
        <Type Value="Include"/>
      </Item115>
      <Item116>
        <Filename Value="../src/base_rendering/castleglimages_packing.inc"/>
        <Type Value="Include"/>
      </Item116>
      <Item117>
        <Filename Value="../src/base_rendering/castleglimages_persistentimage.inc"/>
        <Type Value="Include"/>
      </Item117>
      <Item118>
        <Filename Value="../src/base_rendering/castleglimages_rendertotexture.inc"/>
        <Type Value="Include"/>
      </Item118>
      <Item119>
        <Filename Value="../src/base_rendering/castleglimages_savescreen.inc"/>
        <Type Value="Include"/>
      </Item119>
      <Item120>
        <Filename Value="../src/base_rendering/castleglimages_sprite.inc"/>
        <Type Value="Include"/>
      </Item120>
      <Item121>
        <Filename Value="../src/base_rendering/castleglimages_texturememoryprofiler.inc"/>
        <Type Value="Include"/>
      </Item121>
      <Item122>
        <Filename Value="../src/base_rendering/castleglimages_video.inc"/>
        <Type Value="Include"/>
      </Item122>
      <Item123>
        <Filename Value="../src/base_rendering/castleglimages_wrap.inc"/>
        <Type Value="Include"/>
      </Item123>
      <Item124>
        <Filename Value="../src/base_rendering/castleglshaders.pas"/>
        <UnitName Value="CastleGLShaders"/>
      </Item124>
      <Item125>
        <Filename Value="../src/base_rendering/castleglutils.pas"/>
        <UnitName Value="CastleGLUtils"/>
      </Item125>
      <Item126>
        <Filename Value="../src/base_rendering/castleglutils_draw_primitive_2d.inc"/>
        <Type Value="Include"/>
      </Item126>
      <Item127>
        <Filename Value="../src/base_rendering/castleglutils_features.inc"/>
        <Type Value="Include"/>
      </Item127>
      <Item128>
        <Filename Value="../src/base_rendering/castleglutils_features_debug.inc"/>
        <Type Value="Include"/>
      </Item128>
      <Item129>
        <Filename Value="../src/base_rendering/castleglutils_information.inc"/>
        <Type Value="Include"/>
      </Item129>
      <Item130>
        <Filename Value="../src/base_rendering/castleglutils_types.inc"/>
        <Type Value="Include"/>
      </Item130>
      <Item131>
        <Filename Value="../src/base_rendering/castleglutils_vertex_array_object.inc"/>
        <Type Value="Include"/>
      </Item131>
      <Item132>
        <Filename Value="../src/base_rendering/castleglversion.pas"/>
        <UnitName Value="CastleGLVersion"/>
      </Item132>
      <Item133>
        <Filename Value="../src/base_rendering/castleinternalcontextbase.pas"/>
        <UnitName Value="CastleInternalContextBase"/>
      </Item133>
      <Item134>
        <Filename Value="../src/base_rendering/castleinternalcontextegl.pas"/>
        <UnitName Value="CastleInternalContextEgl"/>
      </Item134>
      <Item135>
        <Filename Value="../src/base_rendering/castleinternalcontextglx.pas"/>
        <UnitName Value="CastleInternalContextGlx"/>
      </Item135>
      <Item136>
        <Filename Value="../src/base_rendering/castleinternalcontextwgl.pas"/>
        <UnitName Value="CastleInternalContextWgl"/>
      </Item136>
      <Item137>
        <Filename Value="../src/base_rendering/castleinternalegl.pas"/>
        <UnitName Value="CastleInternalEgl"/>
      </Item137>
      <Item138>
        <Filename Value="../src/base_rendering/castleinternalglutils.pas"/>
        <UnitName Value="CastleInternalGLUtils"/>
      </Item138>
      <Item139>
        <Filename Value="../src/base_rendering/castleinternalglutils_create_delete.inc"/>
        <Type Value="Include"/>
      </Item139>
      <Item140>
        <Filename Value="../src/base_rendering/castleinternalglutils_errors.inc"/>
        <Type Value="Include"/>
      </Item140>
      <Item141>
        <Filename Value="../src/base_rendering/castleinternalglutils_helpers.inc"/>
        <Type Value="Include"/>
      </Item141>
      <Item142>
        <Filename Value="../src/base_rendering/castleinternalglutils_mipmaps.inc"/>
        <Type Value="Include"/>
      </Item142>
      <Item143>
        <Filename Value="../src/base_rendering/castlerendercontext.pas"/>
        <UnitName Value="CastleRenderContext"/>
      </Item143>
      <Item144>
        <Filename Value="../src/base_rendering/castlerenderprimitives.pas"/>
        <UnitName Value="CastleRenderPrimitives"/>
      </Item144>
      <Item145>
        <Filename Value="../src/base_rendering/castlerenderprimitives_render_unlit_mesh.inc"/>
        <Type Value="Include"/>
      </Item145>
      <Item146>
        <Filename Value="../src/base_rendering/dglopengl/castlegl.pas"/>
        <UnitName Value="CastleGL"/>
      </Item146>
      <Item147>
        <Filename Value="../src/base_rendering/glsl/generated-pascal/distance_field_font.fs.inc"/>
        <Type Value="Include"/>
      </Item147>
      <Item148>
        <Filename Value="../src/base_rendering/glsl/generated-pascal/image.fs.inc"/>
        <Type Value="Include"/>
      </Item148>
      <Item149>
        <Filename Value="../src/base_rendering/glsl/generated-pascal/image.vs.inc"/>
        <Type Value="Include"/>
      </Item149>
      <Item150>
        <Filename Value="../src/base_rendering/glsl/generated-pascal/primitive_2.fs.inc"/>
        <Type Value="Include"/>
      </Item150>
      <Item151>
        <Filename Value="../src/base_rendering/glsl/generated-pascal/primitive_2.vs.inc"/>
        <Type Value="Include"/>
      </Item151>
      <Item152>
        <Filename Value="../src/base_rendering/openglmac.inc"/>
        <Type Value="Include"/>
      </Item152>
      <Item153>
        <Filename Value="../src/castlescript/castlecurves.pas"/>
        <UnitName Value="CastleCurves"/>
      </Item153>
      <Item154>
        <Filename Value="../src/castlescript/castlescript.pas"/>
        <UnitName Value="CastleScript"/>
      </Item154>
      <Item155>
        <Filename Value="../src/castlescript/castlescriptarrays.pas"/>
        <UnitName Value="CastleScriptArrays"/>
      </Item155>
      <Item156>
        <Filename Value="../src/castlescript/castlescriptcorefunctions.pas"/>
        <UnitName Value="CastleScriptCoreFunctions"/>
      </Item156>
      <Item157>
        <Filename Value="../src/castlescript/castlescriptimages.pas"/>
        <UnitName Value="CastleScriptImages"/>
      </Item157>
      <Item158>
        <Filename Value="../src/castlescript/castlescriptlexer.pas"/>
        <UnitName Value="CastleScriptLexer"/>
      </Item158>
      <Item159>
        <Filename Value="../src/castlescript/castlescriptparser.pas"/>
        <UnitName Value="CastleScriptParser"/>
      </Item159>
      <Item160>
        <Filename Value="../src/castlescript/castlescriptvectors.pas"/>
        <UnitName Value="CastleScriptVectors"/>
      </Item160>
      <Item161>
        <Filename Value="../src/castlescript/castlescriptxml.pas"/>
        <UnitName Value="CastleScriptXML"/>
      </Item161>
      <Item162>
        <Filename Value="../src/common_includes/castleconf.inc"/>
        <Type Value="Include"/>
      </Item162>
      <Item163>
        <Filename Value="../src/common_includes/norqcheckbegin.inc"/>
        <Type Value="Include"/>
      </Item163>
      <Item164>
        <Filename Value="../src/common_includes/norqcheckend.inc"/>
        <Type Value="Include"/>
      </Item164>
      <Item165>
        <Filename Value="../src/deprecated_units/auto_generated_persistent_vectors/tcastleonscreenmenu_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item165>
      <Item166>
        <Filename Value="../src/deprecated_units/castle2dscenemanager.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="Castle2DSceneManager"/>
      </Item166>
      <Item167>
        <Filename Value="../src/deprecated_units/castle3d.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="Castle3D"/>
      </Item167>
      <Item168>
        <Filename Value="../src/deprecated_units/castlecreatures.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleCreatures"/>
      </Item168>
      <Item169>
        <Filename Value="../src/deprecated_units/castledialogstates.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleDialogStates"/>
      </Item169>
      <Item170>
        <Filename Value="../src/deprecated_units/castlefontfamily.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleFontFamily"/>
      </Item170>
      <Item171>
        <Filename Value="../src/deprecated_units/castlegamenotifications.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleGameNotifications"/>
      </Item171>
      <Item172>
        <Filename Value="../src/deprecated_units/castleglcontainer.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleGLContainer"/>
      </Item172>
      <Item173>
        <Filename Value="../src/deprecated_units/castlegoogleplaygames.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleGooglePlayGames"/>
      </Item173>
      <Item174>
        <Filename Value="../src/deprecated_units/castleinternalusedeprecatedunits.pas"/>
        <UnitName Value="CastleInternalUseDeprecatedUnits"/>
      </Item174>
      <Item175>
        <Filename Value="../src/deprecated_units/castleitems.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleItems"/>
      </Item175>
      <Item176>
        <Filename Value="../src/deprecated_units/castlelevels.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleLevels"/>
      </Item176>
      <Item177>
        <Filename Value="../src/deprecated_units/castlelocalization.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleLocalization"/>
      </Item177>
      <Item178>
        <Filename Value="../src/deprecated_units/castlelocalization_castlecore.inc"/>
        <Type Value="Include"/>
      </Item178>
      <Item179>
        <Filename Value="../src/deprecated_units/castlelocalizationfileloader.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleLocalizationFileLoader"/>
      </Item179>
      <Item180>
        <Filename Value="../src/deprecated_units/castlematerialproperties.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleMaterialProperties"/>
      </Item180>
      <Item181>
        <Filename Value="../src/deprecated_units/castleonscreenmenu.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleOnScreenMenu"/>
      </Item181>
      <Item182>
        <Filename Value="../src/deprecated_units/castleplayer.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastlePlayer"/>
      </Item182>
      <Item183>
        <Filename Value="../src/deprecated_units/castleprogress.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleProgress"/>
      </Item183>
      <Item184>
        <Filename Value="../src/deprecated_units/castleprogressconsole.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleProgressConsole"/>
      </Item184>
      <Item185>
        <Filename Value="../src/deprecated_units/castlerenderer.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleRenderer"/>
      </Item185>
      <Item186>
        <Filename Value="../src/deprecated_units/castlerendererbasetypes.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleRendererBaseTypes"/>
      </Item186>
      <Item187>
        <Filename Value="../src/deprecated_units/castleresources.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleResources"/>
      </Item187>
      <Item188>
        <Filename Value="../src/deprecated_units/castlescenemanager.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleSceneManager"/>
      </Item188>
      <Item189>
        <Filename Value="../src/deprecated_units/castleshaders.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleShaders"/>
      </Item189>
      <Item190>
        <Filename Value="../src/deprecated_units/castlesoundallocator.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleSoundAllocator"/>
      </Item190>
      <Item191>
        <Filename Value="../src/deprecated_units/castletransformextra.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleTransformExtra"/>
      </Item191>
      <Item192>
        <Filename Value="../src/deprecated_units/castleuistate.pas"/>
        <UnitName Value="CastleUIState"/>
      </Item192>
      <Item193>
        <Filename Value="../src/deprecated_units/castlewarnings.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleWarnings"/>
      </Item193>
      <Item194>
        <Filename Value="../src/deprecated_units/castlewindowsfonts.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleWindowsFonts"/>
      </Item194>
      <Item195>
        <Filename Value="../src/files/castlecomponentserialize.pas"/>
        <UnitName Value="CastleComponentSerialize"/>
      </Item195>
      <Item196>
        <Filename Value="../src/files/castleconfig.pas"/>
        <UnitName Value="CastleConfig"/>
      </Item196>
      <Item197>
        <Filename Value="../src/files/castledownload.pas"/>
        <UnitName Value="CastleDownload"/>
      </Item197>
      <Item198>
        <Filename Value="../src/files/castledownload_asynchronous.inc"/>
        <Type Value="Include"/>
      </Item198>
      <Item199>
        <Filename Value="../src/files/castledownload_internal_utils.inc"/>
        <Type Value="Include"/>
      </Item199>
      <Item200>
        <Filename Value="../src/files/castledownload_mime.inc"/>
        <Type Value="Include"/>
      </Item200>
      <Item201>
        <Filename Value="../src/files/castledownload_register.inc"/>
        <Type Value="Include"/>
      </Item201>
      <Item202>
        <Filename Value="../src/files/castledownload_save.inc"/>
        <Type Value="Include"/>
      </Item202>
      <Item203>
        <Filename Value="../src/files/castledownload_strings_helper.inc"/>
        <Type Value="Include"/>
      </Item203>
      <Item204>
        <Filename Value="../src/files/castledownload_synchronous.inc"/>
        <Type Value="Include"/>
      </Item204>
      <Item205>
        <Filename Value="../src/files/castledownload_text.inc"/>
        <Type Value="Include"/>
      </Item205>
      <Item206>
        <Filename Value="../src/files/castledownload_url_castleandroidassets.inc"/>
        <Type Value="Include"/>
      </Item206>
      <Item207>
        <Filename Value="../src/files/castledownload_url_castledata.inc"/>
        <Type Value="Include"/>
      </Item207>
      <Item208>
        <Filename Value="../src/files/castledownload_url_castlescript.inc"/>
        <Type Value="Include"/>
      </Item208>
      <Item209>
        <Filename Value="../src/files/castledownload_url_compiled.inc"/>
        <Type Value="Include"/>
      </Item209>
      <Item210>
        <Filename Value="../src/files/castledownload_url_data.inc"/>
        <Type Value="Include"/>
      </Item210>
      <Item211>
        <Filename Value="../src/files/castledownload_url_ecmascript.inc"/>
        <Type Value="Include"/>
      </Item211>
      <Item212>
        <Filename Value="../src/files/castledownload_url_file.inc"/>
        <Type Value="Include"/>
      </Item212>
      <Item213>
        <Filename Value="../src/files/castledownload_url_http_android.inc"/>
        <Type Value="Include"/>
      </Item213>
      <Item214>
        <Filename Value="../src/files/castledownload_url_http_delphi_net.inc"/>
        <Type Value="Include"/>
      </Item214>
      <Item215>
        <Filename Value="../src/files/castledownload_url_http_fphttpclient.inc"/>
        <Type Value="Include"/>
      </Item215>
      <Item216>
        <Filename Value="../src/files/castledownload_url_http_indy.inc"/>
        <Type Value="Include"/>
      </Item216>
      <Item217>
        <Filename Value="../src/files/castledownload_utils.inc"/>
        <Type Value="Include"/>
      </Item217>
      <Item218>
        <Filename Value="../src/files/castlefilefilters.pas"/>
        <UnitName Value="CastleFileFilters"/>
      </Item218>
      <Item219>
        <Filename Value="../src/files/castlefilesutils.pas"/>
        <UnitName Value="CastleFilesUtils"/>
      </Item219>
      <Item220>
        <Filename Value="../src/files/castlefindfiles.pas"/>
        <UnitName Value="CastleFindFiles"/>
      </Item220>
      <Item221>
        <Filename Value="../src/files/castleinternaldatauri.pas"/>
        <UnitName Value="CastleInternalDataUri"/>
      </Item221>
      <Item222>
        <Filename Value="../src/files/castleinternaldirectoryinformation.pas"/>
        <UnitName Value="CastleInternalDirectoryInformation"/>
      </Item222>
      <Item223>
        <Filename Value="../src/files/castleinternalfilemonitor.pas"/>
        <UnitName Value="CastleInternalFileMonitor"/>
      </Item223>
      <Item224>
        <Filename Value="../src/files/castleinternalurlutils.pas"/>
        <UnitName Value="CastleInternalUrlUtils"/>
      </Item224>
      <Item225>
        <Filename Value="../src/files/castlelocalizationgettext.pas"/>
        <UnitName Value="CastleLocalizationGetText"/>
      </Item225>
      <Item226>
        <Filename Value="../src/files/castlerecentfiles.pas"/>
        <UnitName Value="CastleRecentFiles"/>
      </Item226>
      <Item227>
        <Filename Value="../src/files/castleuriutils.pas"/>
        <UnitName Value="CastleUriUtils"/>
      </Item227>
      <Item228>
        <Filename Value="../src/files/castlexmlcfginternal.pas"/>
        <UnitName Value="CastleXMLCfgInternal"/>
      </Item228>
      <Item229>
        <Filename Value="../src/files/castlexmlconfig.pas"/>
        <UnitName Value="CastleXMLConfig"/>
      </Item229>
      <Item230>
        <Filename Value="../src/files/castlexmlutils.pas"/>
        <UnitName Value="CastleXmlUtils"/>
      </Item230>
      <Item231>
        <Filename Value="../src/files/castlezip.pas"/>
        <UnitName Value="CastleZip"/>
      </Item231>
      <Item232>
        <Filename Value="../src/files/tools/castleinternalarchitectures.pas"/>
        <UnitName Value="CastleInternalArchitectures"/>
      </Item232>
      <Item233>
        <Filename Value="../src/files/tools/castleinternalprojectlocalsettings.pas"/>
        <UnitName Value="CastleInternalProjectLocalSettings"/>
      </Item233>
      <Item234>
        <Filename Value="../src/files/tools/castleinternaltools.pas"/>
        <UnitName Value="CastleInternalTools"/>
      </Item234>
      <Item235>
        <Filename Value="../src/fonts/castlefonts.pas"/>
        <UnitName Value="CastleFonts"/>
      </Item235>
      <Item236>
        <Filename Value="../src/fonts/castlefonts_abstractfont.inc"/>
        <Type Value="Include"/>
      </Item236>
      <Item237>
        <Filename Value="../src/fonts/castlefonts_bitmapfont.inc"/>
        <Type Value="Include"/>
      </Item237>
      <Item238>
        <Filename Value="../src/fonts/castlefonts_font.inc"/>
        <Type Value="Include"/>
      </Item238>
      <Item239>
        <Filename Value="../src/fonts/castlefonts_fontfamily.inc"/>
        <Type Value="Include"/>
      </Item239>
      <Item240>
        <Filename Value="../src/fonts/castlefonts_fontsizevariants.inc"/>
        <Type Value="Include"/>
      </Item240>
      <Item241>
        <Filename Value="../src/fonts/castlefonts_miscellaneous.inc"/>
        <Type Value="Include"/>
      </Item241>
      <Item242>
        <Filename Value="../src/fonts/castleinternalfreetype.pas"/>
        <UnitName Value="CastleInternalFreeType"/>
      </Item242>
      <Item243>
        <Filename Value="../src/fonts/castleinternalfreetypeh.pas"/>
        <UnitName Value="CastleInternalFreeTypeH"/>
      </Item243>
      <Item244>
        <Filename Value="../src/fonts/castleinternalrichtext.pas"/>
        <UnitName Value="CastleInternalRichText"/>
      </Item244>
      <Item245>
        <Filename Value="../src/fonts/castletexturefont_default3d_sans.pas"/>
        <UnitName Value="CastleTextureFont_Default3D_Sans"/>
      </Item245>
      <Item246>
        <Filename Value="../src/fonts/castletexturefont_defaultui.pas"/>
        <UnitName Value="CastleTextureFont_DefaultUi"/>
      </Item246>
      <Item247>
        <Filename Value="../src/fonts/castletexturefontdata.pas"/>
        <UnitName Value="CastleTextureFontData"/>
      </Item247>
      <Item248>
        <Filename Value="../src/images/castleimages.pas"/>
        <UnitName Value="CastleImages"/>
      </Item248>
      <Item249>
        <Filename Value="../src/images/castleimages_assign.inc"/>
        <Type Value="Include"/>
      </Item249>
      <Item250>
        <Filename Value="../src/images/castleimages_astc.inc"/>
        <Type Value="Include"/>
      </Item250>
      <Item251>
        <Filename Value="../src/images/castleimages_class_gpu_compressed.inc"/>
        <Type Value="Include"/>
      </Item251>
      <Item252>
        <Filename Value="../src/images/castleimages_class_grayscale.inc"/>
        <Type Value="Include"/>
      </Item252>
      <Item253>
        <Filename Value="../src/images/castleimages_class_grayscale_alpha.inc"/>
        <Type Value="Include"/>
      </Item253>
      <Item254>
        <Filename Value="../src/images/castleimages_class_grayscale_alpha_float.inc"/>
        <Type Value="Include"/>
      </Item254>
      <Item255>
        <Filename Value="../src/images/castleimages_class_grayscale_float.inc"/>
        <Type Value="Include"/>
      </Item255>
      <Item256>
        <Filename Value="../src/images/castleimages_class_rgb.inc"/>
        <Type Value="Include"/>
      </Item256>
      <Item257>
        <Filename Value="../src/images/castleimages_class_rgb_alpha.inc"/>
        <Type Value="Include"/>
      </Item257>
      <Item258>
        <Filename Value="../src/images/castleimages_class_rgb_alpha_float.inc"/>
        <Type Value="Include"/>
      </Item258>
      <Item259>
        <Filename Value="../src/images/castleimages_class_rgb_float.inc"/>
        <Type Value="Include"/>
      </Item259>
      <Item260>
        <Filename Value="../src/images/castleimages_composite.inc"/>
        <Type Value="Include"/>
      </Item260>
      <Item261>
        <Filename Value="../src/images/castleimages_draw.inc"/>
        <Type Value="Include"/>
      </Item261>
      <Item262>
        <Filename Value="../src/images/castleimages_file_formats.inc"/>
        <Type Value="Include"/>
      </Item262>
      <Item263>
        <Filename Value="../src/images/castleimages_fpimage.inc"/>
        <Type Value="Include"/>
      </Item263>
      <Item264>
        <Filename Value="../src/images/castleimages_libpng.inc"/>
        <Type Value="Include"/>
      </Item264>
      <Item265>
        <Filename Value="../src/images/castleimages_loading_saving_func.inc"/>
        <Type Value="Include"/>
      </Item265>
      <Item266>
        <Filename Value="../src/images/castleimages_modulatergb_implement.inc"/>
        <Type Value="Include"/>
      </Item266>
      <Item267>
        <Filename Value="../src/images/castleimages_paint.inc"/>
        <Type Value="Include"/>
      </Item267>
      <Item268>
        <Filename Value="../src/images/castleimages_png.inc"/>
        <Type Value="Include"/>
      </Item268>
      <Item269>
        <Filename Value="../src/images/castleimages_s3tc_flip_vertical.inc"/>
        <Type Value="Include"/>
      </Item269>
      <Item270>
        <Filename Value="../src/images/castleimages_transformrgb_implement.inc"/>
        <Type Value="Include"/>
      </Item270>
      <Item271>
        <Filename Value="../src/images/castleimages_vampyre_imaging.inc"/>
        <Type Value="Include"/>
      </Item271>
      <Item272>
        <Filename Value="../src/images/castleimages_vcl_imaging.inc"/>
        <Type Value="Include"/>
      </Item272>
      <Item273>
        <Filename Value="../src/images/castleinternalautogenerated.pas"/>
        <UnitName Value="CastleInternalAutoGenerated"/>
      </Item273>
      <Item274>
        <Filename Value="../src/images/castleinternalcompositeimage.pas"/>
        <UnitName Value="CastleInternalCompositeImage"/>
      </Item274>
      <Item275>
        <Filename Value="../src/images/castleinternalcompositeimage_dds.inc"/>
        <Type Value="Include"/>
      </Item275>
      <Item276>
        <Filename Value="../src/images/castleinternalcompositeimage_format_handler.inc"/>
        <Type Value="Include"/>
      </Item276>
      <Item277>
        <Filename Value="../src/images/castleinternalcompositeimage_ktx.inc"/>
        <Type Value="Include"/>
      </Item277>
      <Item278>
        <Filename Value="../src/images/castleinternaldatacompression.pas"/>
        <UnitName Value="CastleInternalDataCompression"/>
      </Item278>
      <Item279>
        <Filename Value="../src/images/castleinternalpng.pas"/>
        <UnitName Value="CastleInternalPng"/>
      </Item279>
      <Item280>
        <Filename Value="../src/images/castleinternalpng_dynamic.inc"/>
        <Type Value="Include"/>
      </Item280>
      <Item281>
        <Filename Value="../src/images/castleinternalpng_static.inc"/>
        <Type Value="Include"/>
      </Item281>
      <Item282>
        <Filename Value="../src/images/castletextureimages.pas"/>
        <UnitName Value="CastleTextureImages"/>
      </Item282>
      <Item283>
        <Filename Value="../src/images/castlevideos.pas"/>
        <UnitName Value="CastleVideos"/>
      </Item283>
      <Item284>
        <Filename Value="../src/physics/kraft/castleinternalkraftoverrides.pas"/>
        <UnitName Value="CastleInternalKraftOverrides"/>
      </Item284>
      <Item285>
        <Filename Value="../src/physics/kraft/castlekraft.inc"/>
        <Type Value="Include"/>
      </Item285>
      <Item286>
        <Filename Value="../src/physics/kraft/kraft.pas"/>
        <UnitName Value="kraft"/>
      </Item286>
      <Item287>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastleabstractprimitive_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item287>
      <Item288>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastlebackground_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item288>
      <Item289>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastlebox_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item289>
      <Item290>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastledirectionallight_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item290>
      <Item291>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastlefog_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item291>
      <Item292>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastleimagetransform_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item292>
      <Item293>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastleplane_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item293>
      <Item294>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastlepointlight_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item294>
      <Item295>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastlespotlight_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item295>
      <Item296>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastleterrain_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item296>
      <Item297>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastleterrainlayer_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item297>
      <Item298>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastlethirdpersonnavigation_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item298>
      <Item299>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastletiledmapcontrol_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item299>
      <Item300>
        <Filename Value="../src/scene/auto_generated_persistent_vectors/tcastleviewport_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item300>
      <Item301>
        <Filename Value="../src/scene/castledebugtransform.pas"/>
        <UnitName Value="CastleDebugTransform"/>
      </Item301>
      <Item302>
        <Filename Value="../src/scene/castleinternalarraysgenerator.pas"/>
        <UnitName Value="CastleInternalArraysGenerator"/>
      </Item302>
      <Item303>
        <Filename Value="../src/scene/castleinternalarraysgenerator_geometry3d.inc"/>
        <Type Value="Include"/>
      </Item303>
      <Item304>
        <Filename Value="../src/scene/castleinternalarraysgenerator_rendering.inc"/>
        <Type Value="Include"/>
      </Item304>
      <Item305>
        <Filename Value="../src/scene/castleinternalbackgroundrenderer.pas"/>
        <UnitName Value="CastleInternalBackgroundRenderer"/>
      </Item305>
      <Item306>
        <Filename Value="../src/scene/castleinternalbatchshapes.pas"/>
        <UnitName Value="CastleInternalBatchShapes"/>
      </Item306>
      <Item307>
        <Filename Value="../src/scene/castleinternalglcubemaps.pas"/>
        <UnitName Value="CastleInternalGLCubeMaps"/>
      </Item307>
      <Item308>
        <Filename Value="../src/scene/castleinternalmaterialproperties.pas"/>
        <UnitName Value="CastleInternalMaterialProperties"/>
      </Item308>
      <Item309>
        <Filename Value="../src/scene/castleinternalnodeinterpolator.pas"/>
        <UnitName Value="CastleInternalNodeInterpolator"/>
      </Item309>
      <Item310>
        <Filename Value="../src/scene/castleinternalnoise.pas"/>
        <UnitName Value="CastleInternalNoise"/>
      </Item310>
      <Item311>
        <Filename Value="../src/scene/castleinternalnoise_interpolatednoise2d_linear_cosine.inc"/>
        <Type Value="Include"/>
      </Item311>
      <Item312>
        <Filename Value="../src/scene/castleinternalnoise_interpolatednoise2d_spline.inc"/>
        <Type Value="Include"/>
      </Item312>
      <Item313>
        <Filename Value="../src/scene/castleinternalnormals.pas"/>
        <UnitName Value="CastleInternalNormals"/>
      </Item313>
      <Item314>
        <Filename Value="../src/scene/castleinternalocclusionculling.pas"/>
        <UnitName Value="CastleInternalOcclusionCulling"/>
      </Item314>
      <Item315>
        <Filename Value="../src/scene/castleinternalrenderer.pas"/>
        <UnitName Value="CastleInternalRenderer"/>
      </Item315>
      <Item316>
        <Filename Value="../src/scene/castleinternalrenderer_cache.inc"/>
        <Type Value="Include"/>
      </Item316>
      <Item317>
        <Filename Value="../src/scene/castleinternalrenderer_cache_types.inc"/>
        <Type Value="Include"/>
      </Item317>
      <Item318>
        <Filename Value="../src/scene/castleinternalrenderer_custom_shaders.inc"/>
        <Type Value="Include"/>
      </Item318>
      <Item319>
        <Filename Value="../src/scene/castleinternalrenderer_final_globals.inc"/>
        <Type Value="Include"/>
      </Item319>
      <Item320>
        <Filename Value="../src/scene/castleinternalrenderer_glsl.inc"/>
        <Type Value="Include"/>
      </Item320>
      <Item321>
        <Filename Value="../src/scene/castleinternalrenderer_initial_types.inc"/>
        <Type Value="Include"/>
      </Item321>
      <Item322>
        <Filename Value="../src/scene/castleinternalrenderer_materials.inc"/>
        <Type Value="Include"/>
      </Item322>
      <Item323>
        <Filename Value="../src/scene/castleinternalrenderer_meshrenderer.inc"/>
        <Type Value="Include"/>
      </Item323>
      <Item324>
        <Filename Value="../src/scene/castleinternalrenderer_pass.inc"/>
        <Type Value="Include"/>
      </Item324>
      <Item325>
        <Filename Value="../src/scene/castleinternalrenderer_renderer.inc"/>
        <Type Value="Include"/>
      </Item325>
      <Item326>
        <Filename Value="../src/scene/castleinternalrenderer_resource.inc"/>
        <Type Value="Include"/>
      </Item326>
      <Item327>
        <Filename Value="../src/scene/castleinternalrenderer_screen_effects.inc"/>
        <Type Value="Include"/>
      </Item327>
      <Item328>
        <Filename Value="../src/scene/castleinternalrenderer_shape.inc"/>
        <Type Value="Include"/>
      </Item328>
      <Item329>
        <Filename Value="../src/scene/castleinternalrenderer_surfacetextures.inc"/>
        <Type Value="Include"/>
      </Item329>
      <Item330>
        <Filename Value="../src/scene/castleinternalrenderer_texture.inc"/>
        <Type Value="Include"/>
      </Item330>
      <Item331>
        <Filename Value="../src/scene/castleinternalscreeneffects.pas"/>
        <UnitName Value="CastleInternalScreenEffects"/>
      </Item331>
      <Item332>
        <Filename Value="../src/scene/castleinternalshadowmaps.pas"/>
        <UnitName Value="CastleInternalShadowMaps"/>
      </Item332>
      <Item333>
        <Filename Value="../src/scene/castleinternalshapeoctree.pas"/>
        <UnitName Value="CastleInternalShapeOctree"/>
      </Item333>
      <Item334>
        <Filename Value="../src/scene/castleinternalshapesrenderer.pas"/>
        <UnitName Value="CastleInternalShapesRenderer"/>
      </Item334>
      <Item335>
        <Filename Value="../src/scene/castleinternalspritesheet.pas"/>
        <UnitName Value="CastleInternalSpriteSheet"/>
      </Item335>
      <Item336>
        <Filename Value="../src/scene/castleinternaltransformdata.pas"/>
        <UnitName Value="CastleInternalTransformData"/>
      </Item336>
      <Item337>
        <Filename Value="../src/scene/castleinternaltriangleoctree.pas"/>
        <UnitName Value="CastleInternalTriangleOctree"/>
      </Item337>
      <Item338>
        <Filename Value="../src/scene/castleinternaltriangleoctree_raysegmentcollisions.inc"/>
        <Type Value="Include"/>
      </Item338>
      <Item339>
        <Filename Value="../src/scene/castleinternalx3dlexer.pas"/>
        <UnitName Value="CastleInternalX3DLexer"/>
      </Item339>
      <Item340>
        <Filename Value="../src/scene/castleinternalx3dscript.pas"/>
        <UnitName Value="CastleInternalX3DScript"/>
      </Item340>
      <Item341>
        <Filename Value="../src/scene/castlelivingbehaviors.pas"/>
        <UnitName Value="CastleLivingBehaviors"/>
      </Item341>
      <Item342>
        <Filename Value="../src/scene/castlelivingbehaviors_damage.inc"/>
        <Type Value="Include"/>
      </Item342>
      <Item343>
        <Filename Value="../src/scene/castlelivingbehaviors_living.inc"/>
        <Type Value="Include"/>
      </Item343>
      <Item344>
        <Filename Value="../src/scene/castlelivingbehaviors_missile.inc"/>
        <Type Value="Include"/>
      </Item344>
      <Item345>
        <Filename Value="../src/scene/castlelivingbehaviors_moveattack.inc"/>
        <Type Value="Include"/>
      </Item345>
      <Item346>
        <Filename Value="../src/scene/castleraytracer.pas"/>
        <UnitName Value="CastleRayTracer"/>
      </Item346>
      <Item347>
        <Filename Value="../src/scene/castlerendererinternallights.pas"/>
        <UnitName Value="CastleRendererInternalLights"/>
      </Item347>
      <Item348>
        <Filename Value="../src/scene/castlerendererinternalshader.pas"/>
        <UnitName Value="CastleRendererInternalShader"/>
      </Item348>
      <Item349>
        <Filename Value="../src/scene/castlerendererinternalshader_bumpmapping.inc"/>
        <Type Value="Include"/>
      </Item349>
      <Item350>
        <Filename Value="../src/scene/castlerendererinternalshader_hash.inc"/>
        <Type Value="Include"/>
      </Item350>
      <Item351>
        <Filename Value="../src/scene/castlerendererinternalshader_light.inc"/>
        <Type Value="Include"/>
      </Item351>
      <Item352>
        <Filename Value="../src/scene/castlerendererinternalshader_mirrorplane.inc"/>
        <Type Value="Include"/>
      </Item352>
      <Item353>
        <Filename Value="../src/scene/castlerendererinternalshader_shadowmap.inc"/>
        <Type Value="Include"/>
      </Item353>
      <Item354>
        <Filename Value="../src/scene/castlerendererinternalshader_surfacetexture.inc"/>
        <Type Value="Include"/>
      </Item354>
      <Item355>
        <Filename Value="../src/scene/castlerendererinternalshader_texture.inc"/>
        <Type Value="Include"/>
      </Item355>
      <Item356>
        <Filename Value="../src/scene/castlerendererinternaltextureenv.pas"/>
        <UnitName Value="CastleRendererInternalTextureEnv"/>
      </Item356>
      <Item357>
        <Filename Value="../src/scene/castlescene.pas"/>
        <UnitName Value="CastleScene"/>
      </Item357>
      <Item358>
        <Filename Value="../src/scene/castlescene_abstractlight.inc"/>
        <Type Value="Include"/>
      </Item358>
      <Item359>
        <Filename Value="../src/scene/castlescene_abstractprimitive.inc"/>
        <Type Value="Include"/>
      </Item359>
      <Item360>
        <Filename Value="../src/scene/castlescene_background.inc"/>
        <Type Value="Include"/>
      </Item360>
      <Item361>
        <Filename Value="../src/scene/castlescene_box.inc"/>
        <Type Value="Include"/>
      </Item361>
      <Item362>
        <Filename Value="../src/scene/castlescene_cone.inc"/>
        <Type Value="Include"/>
      </Item362>
      <Item363>
        <Filename Value="../src/scene/castlescene_cylinder.inc"/>
        <Type Value="Include"/>
      </Item363>
      <Item364>
        <Filename Value="../src/scene/castlescene_directionallight.inc"/>
        <Type Value="Include"/>
      </Item364>
      <Item365>
        <Filename Value="../src/scene/castlescene_editorgizmo.inc"/>
        <Type Value="Include"/>
      </Item365>
      <Item366>
        <Filename Value="../src/scene/castlescene_environmentlight.inc"/>
        <Type Value="Include"/>
      </Item366>
      <Item367>
        <Filename Value="../src/scene/castlescene_fog.inc"/>
        <Type Value="Include"/>
      </Item367>
      <Item368>
        <Filename Value="../src/scene/castlescene_imagetransform.inc"/>
        <Type Value="Include"/>
      </Item368>
      <Item369>
        <Filename Value="../src/scene/castlescene_plane.inc"/>
        <Type Value="Include"/>
      </Item369>
      <Item370>
        <Filename Value="../src/scene/castlescene_pointlight.inc"/>
        <Type Value="Include"/>
      </Item370>
      <Item371>
        <Filename Value="../src/scene/castlescene_punctuallight.inc"/>
        <Type Value="Include"/>
      </Item371>
      <Item372>
        <Filename Value="../src/scene/castlescene_roottransform.inc"/>
        <Type Value="Include"/>
      </Item372>
      <Item373>
        <Filename Value="../src/scene/castlescene_sphere.inc"/>
        <Type Value="Include"/>
      </Item373>
      <Item374>
        <Filename Value="../src/scene/castlescene_spotlight.inc"/>
        <Type Value="Include"/>
      </Item374>
      <Item375>
        <Filename Value="../src/scene/castlescene_text.inc"/>
        <Type Value="Include"/>
      </Item375>
      <Item376>
        <Filename Value="../src/scene/castlescenecore.pas"/>
        <UnitName Value="CastleSceneCore"/>
      </Item376>
      <Item377>
        <Filename Value="../src/scene/castlescenecore_collisions.inc"/>
        <Type Value="Include"/>
      </Item377>
      <Item378>
        <Filename Value="../src/scene/castlesceneinternalblending.pas"/>
        <UnitName Value="CastleSceneInternalBlending"/>
      </Item378>
      <Item379>
        <Filename Value="../src/scene/castlesceneinternalshape.pas"/>
        <UnitName Value="CastleSceneInternalShape"/>
      </Item379>
      <Item380>
        <Filename Value="../src/scene/castlescreeneffects.pas"/>
        <UnitName Value="CastleScreenEffects"/>
      </Item380>
      <Item381>
        <Filename Value="../src/scene/castleshapeinternalrendershadowvolumes.pas"/>
        <UnitName Value="CastleShapeInternalRenderShadowVolumes"/>
      </Item381>
      <Item382>
        <Filename Value="../src/scene/castleshapeinternalshadowvolumes.pas"/>
        <UnitName Value="CastleShapeInternalShadowVolumes"/>
      </Item382>
      <Item383>
        <Filename Value="../src/scene/castleshapes.pas"/>
        <UnitName Value="CastleShapes"/>
      </Item383>
      <Item384>
        <Filename Value="../src/scene/castleterrain.pas"/>
        <UnitName Value="CastleTerrain"/>
      </Item384>
      <Item385>
        <Filename Value="../src/scene/castlethirdpersonnavigation.pas"/>
        <UnitName Value="CastleThirdPersonNavigation"/>
      </Item385>
      <Item386>
        <Filename Value="../src/scene/castletiledmap.pas"/>
        <UnitName Value="CastleTiledMap"/>
      </Item386>
      <Item387>
        <Filename Value="../src/scene/castletiledmap_control.inc"/>
        <Type Value="Include"/>
      </Item387>
      <Item388>
        <Filename Value="../src/scene/castletiledmap_data.inc"/>
        <Type Value="Include"/>
      </Item388>
      <Item389>
        <Filename Value="../src/scene/castletiledmap_scene.inc"/>
        <Type Value="Include"/>
      </Item389>
      <Item390>
        <Filename Value="../src/scene/castletransformmanipulate.pas"/>
        <UnitName Value="CastleTransformManipulate"/>
      </Item390>
      <Item391>
        <Filename Value="../src/scene/castleviewport.pas"/>
        <UnitName Value="CastleViewport"/>
      </Item391>
      <Item392>
        <Filename Value="../src/scene/castleviewport_autonavigation.inc"/>
        <Type Value="Include"/>
      </Item392>
      <Item393>
        <Filename Value="../src/scene/castleviewport_design_navigation.inc"/>
        <Type Value="Include"/>
      </Item393>
      <Item394>
        <Filename Value="../src/scene/castleviewport_scenemanager.inc"/>
        <Type Value="Include"/>
      </Item394>
      <Item395>
        <Filename Value="../src/scene/castleviewport_serialize.inc"/>
        <Type Value="Include"/>
      </Item395>
      <Item396>
        <Filename Value="../src/scene/castleviewport_touchnavigation.inc"/>
        <Type Value="Include"/>
      </Item396>
      <Item397>
        <Filename Value="../src/scene/castleviewport_warmup_cache.inc"/>
        <Type Value="Include"/>
      </Item397>
      <Item398>
        <Filename Value="../src/scene/glsl/generated-pascal/bump_mapping.fs.inc"/>
        <Type Value="Include"/>
      </Item398>
      <Item399>
        <Filename Value="../src/scene/glsl/generated-pascal/bump_mapping.vs.inc"/>
        <Type Value="Include"/>
      </Item399>
      <Item400>
        <Filename Value="../src/scene/glsl/generated-pascal/bump_mapping_parallax.fs.inc"/>
        <Type Value="Include"/>
      </Item400>
      <Item401>
        <Filename Value="../src/scene/glsl/generated-pascal/bump_mapping_parallax.vs.inc"/>
        <Type Value="Include"/>
      </Item401>
      <Item402>
        <Filename Value="../src/scene/glsl/generated-pascal/bump_mapping_steep_parallax_shadowing.fs.inc"/>
        <Type Value="Include"/>
      </Item402>
      <Item403>
        <Filename Value="../src/scene/glsl/generated-pascal/bump_mapping_steep_parallax_shadowing.vs.inc"/>
        <Type Value="Include"/>
      </Item403>
      <Item404>
        <Filename Value="../src/scene/glsl/generated-pascal/common.fs.inc"/>
        <Type Value="Include"/>
      </Item404>
      <Item405>
        <Filename Value="../src/scene/glsl/generated-pascal/common.vs.inc"/>
        <Type Value="Include"/>
      </Item405>
      <Item406>
        <Filename Value="../src/scene/glsl/generated-pascal/fallback.fs.inc"/>
        <Type Value="Include"/>
      </Item406>
      <Item407>
        <Filename Value="../src/scene/glsl/generated-pascal/fallback.vs.inc"/>
        <Type Value="Include"/>
      </Item407>
      <Item408>
        <Filename Value="../src/scene/glsl/generated-pascal/geometry_shader_utils.gs.inc"/>
        <Type Value="Include"/>
      </Item408>
      <Item409>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_phong_add_light.glsl.inc"/>
        <Type Value="Include"/>
      </Item409>
      <Item410>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_phong_shading_gouraud.vs.inc"/>
        <Type Value="Include"/>
      </Item410>
      <Item411>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_phong_shading_phong.fs.inc"/>
        <Type Value="Include"/>
      </Item411>
      <Item412>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_phong_structures.glsl.inc"/>
        <Type Value="Include"/>
      </Item412>
      <Item413>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_physical_add_light.glsl.inc"/>
        <Type Value="Include"/>
      </Item413>
      <Item414>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_physical_shading_gouraud.vs.inc"/>
        <Type Value="Include"/>
      </Item414>
      <Item415>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_physical_shading_phong.fs.inc"/>
        <Type Value="Include"/>
      </Item415>
      <Item416>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_physical_structures.glsl.inc"/>
        <Type Value="Include"/>
      </Item416>
      <Item417>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_unlit_add_light.glsl.inc"/>
        <Type Value="Include"/>
      </Item417>
      <Item418>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_unlit_shading_gouraud.vs.inc"/>
        <Type Value="Include"/>
      </Item418>
      <Item419>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_unlit_shading_phong.fs.inc"/>
        <Type Value="Include"/>
      </Item419>
      <Item420>
        <Filename Value="../src/scene/glsl/generated-pascal/lighting_model_unlit_structures.glsl.inc"/>
        <Type Value="Include"/>
      </Item420>
      <Item421>
        <Filename Value="../src/scene/glsl/generated-pascal/main_shading_gouraud.fs.inc"/>
        <Type Value="Include"/>
      </Item421>
      <Item422>
        <Filename Value="../src/scene/glsl/generated-pascal/main_shading_gouraud.vs.inc"/>
        <Type Value="Include"/>
      </Item422>
      <Item423>
        <Filename Value="../src/scene/glsl/generated-pascal/main_shading_phong.fs.inc"/>
        <Type Value="Include"/>
      </Item423>
      <Item424>
        <Filename Value="../src/scene/glsl/generated-pascal/main_shading_phong.vs.inc"/>
        <Type Value="Include"/>
      </Item424>
      <Item425>
        <Filename Value="../src/scene/glsl/generated-pascal/screen_effect.vs.inc"/>
        <Type Value="Include"/>
      </Item425>
      <Item426>
        <Filename Value="../src/scene/glsl/generated-pascal/screen_effect_library.glsl.inc"/>
        <Type Value="Include"/>
      </Item426>
      <Item427>
        <Filename Value="../src/scene/glsl/generated-pascal/shadow_map_common.fs.inc"/>
        <Type Value="Include"/>
      </Item427>
      <Item428>
        <Filename Value="../src/scene/glsl/generated-pascal/shadow_map_generate.fs.inc"/>
        <Type Value="Include"/>
      </Item428>
      <Item429>
        <Filename Value="../src/scene/glsl/generated-pascal/shadow_map_generate.vs.inc"/>
        <Type Value="Include"/>
      </Item429>
      <Item430>
        <Filename Value="../src/scene/glsl/generated-pascal/simplest.fs.inc"/>
        <Type Value="Include"/>
      </Item430>
      <Item431>
        <Filename Value="../src/scene/glsl/generated-pascal/simplest.vs.inc"/>
        <Type Value="Include"/>
      </Item431>
      <Item432>
        <Filename Value="../src/scene/glsl/generated-pascal/simplest_unlit.fs.inc"/>
        <Type Value="Include"/>
      </Item432>
      <Item433>
        <Filename Value="../src/scene/glsl/generated-pascal/ssao.glsl.inc"/>
        <Type Value="Include"/>
      </Item433>
      <Item434>
        <Filename Value="../src/scene/glsl/generated-pascal/ssr.glsl.inc"/>
        <Type Value="Include"/>
      </Item434>
      <Item435>
        <Filename Value="../src/scene/glsl/generated-pascal/terrain.fs.inc"/>
        <Type Value="Include"/>
      </Item435>
      <Item436>
        <Filename Value="../src/scene/glsl/generated-pascal/terrain.vs.inc"/>
        <Type Value="Include"/>
      </Item436>
      <Item437>
        <Filename Value="../src/scene/glsl/generated-pascal/tone_mapping.fs.inc"/>
        <Type Value="Include"/>
      </Item437>
      <Item438>
        <Filename Value="../src/scene/glsl/generated-pascal/variance_shadow_map_common.fs.inc"/>
        <Type Value="Include"/>
      </Item438>
      <Item439>
        <Filename Value="../src/scene/load/castleloadgltf.pas"/>
        <UnitName Value="CastleLoadGltf"/>
      </Item439>
      <Item440>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada.pas"/>
        <UnitName Value="X3DLoadInternalCollada"/>
      </Item440>
      <Item441>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_cameras.inc"/>
        <Type Value="Include"/>
      </Item441>
      <Item442>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_childrenlist.inc"/>
        <Type Value="Include"/>
      </Item442>
      <Item443>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_controllers.inc"/>
        <Type Value="Include"/>
      </Item443>
      <Item444>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_effects.inc"/>
        <Type Value="Include"/>
      </Item444>
      <Item445>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_geometries.inc"/>
        <Type Value="Include"/>
      </Item445>
      <Item446>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_images.inc"/>
        <Type Value="Include"/>
      </Item446>
      <Item447>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_indexes.inc"/>
        <Type Value="Include"/>
      </Item447>
      <Item448>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_integerparser.inc"/>
        <Type Value="Include"/>
      </Item448>
      <Item449>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_librarynodes.inc"/>
        <Type Value="Include"/>
      </Item449>
      <Item450>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_lights.inc"/>
        <Type Value="Include"/>
      </Item450>
      <Item451>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_materials.inc"/>
        <Type Value="Include"/>
      </Item451>
      <Item452>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_matrix.inc"/>
        <Type Value="Include"/>
      </Item452>
      <Item453>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_node.inc"/>
        <Type Value="Include"/>
      </Item453>
      <Item454>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_primitives.inc"/>
        <Type Value="Include"/>
      </Item454>
      <Item455>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_read_helpers.inc"/>
        <Type Value="Include"/>
      </Item455>
      <Item456>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_scenes.inc"/>
        <Type Value="Include"/>
      </Item456>
      <Item457>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_source.inc"/>
        <Type Value="Include"/>
      </Item457>
      <Item458>
        <Filename Value="../src/scene/load/collada/x3dloadinternalcollada_sources.inc"/>
        <Type Value="Include"/>
      </Item458>
      <Item459>
        <Filename Value="../src/scene/load/ifc/castleifc.pas"/>
        <UnitName Value="CastleIfc"/>
      </Item459>
      <Item460>
        <Filename Value="../src/scene/load/ifc/castleifc_ifc_standard_types.inc"/>
        <Type Value="Include"/>
      </Item460>
      <Item461>
        <Filename Value="../src/scene/load/ifc/castleifc_ifc_standard_types_autogenerated.inc"/>
        <Type Value="Include"/>
      </Item461>
      <Item462>
        <Filename Value="../src/scene/load/ifc/castleifc_ifc_types.inc"/>
        <Type Value="Include"/>
      </Item462>
      <Item463>
        <Filename Value="../src/scene/load/ifc/castleifc_json.inc"/>
        <Type Value="Include"/>
      </Item463>
      <Item464>
        <Filename Value="../src/scene/load/ifc/castleifc_load_to_x3d.inc"/>
        <Type Value="Include"/>
      </Item464>
      <Item465>
        <Filename Value="../src/scene/load/ifc/castleifc_save_from_x3d.inc"/>
        <Type Value="Include"/>
      </Item465>
      <Item466>
        <Filename Value="../src/scene/load/md3/x3dloadinternalmd3.pas"/>
        <UnitName Value="X3DLoadInternalMD3"/>
      </Item466>
      <Item467>
        <Filename Value="../src/scene/load/md3/x3dloadinternalmd3_animation.inc"/>
        <Type Value="Include"/>
      </Item467>
      <Item468>
        <Filename Value="../src/scene/load/md3/x3dloadinternalmd3_converter.inc"/>
        <Type Value="Include"/>
      </Item468>
      <Item469>
        <Filename Value="../src/scene/load/md3/x3dloadinternalmd3_structs.inc"/>
        <Type Value="Include"/>
      </Item469>
      <Item470>
        <Filename Value="../src/scene/load/md3/x3dloadinternalmd3_surface.inc"/>
        <Type Value="Include"/>
      </Item470>
      <Item471>
        <Filename Value="../src/scene/load/pasgltf/CastlePasDblStrUtils.pas"/>
        <UnitName Value="CastlePasDblStrUtils"/>
      </Item471>
      <Item472>
        <Filename Value="../src/scene/load/pasgltf/CastlePasGLTF.pas"/>
        <UnitName Value="CastlePasGLTF"/>
      </Item472>
      <Item473>
        <Filename Value="../src/scene/load/pasgltf/CastlePasJSON.pas"/>
        <UnitName Value="CastlePasJSON"/>
      </Item473>
      <Item474>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine.pas"/>
        <UnitName Value="X3DLoadInternalSpine"/>
      </Item474>
      <Item475>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_animations.inc"/>
        <Type Value="Include"/>
      </Item475>
      <Item476>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_animutils.inc"/>
        <Type Value="Include"/>
      </Item476>
      <Item477>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_atlas.inc"/>
        <Type Value="Include"/>
      </Item477>
      <Item478>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_attachments.inc"/>
        <Type Value="Include"/>
      </Item478>
      <Item479>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_bones.inc"/>
        <Type Value="Include"/>
      </Item479>
      <Item480>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_bonetimelines.inc"/>
        <Type Value="Include"/>
      </Item480>
      <Item481>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_deformtimelines.inc"/>
        <Type Value="Include"/>
      </Item481>
      <Item482>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_drawordertimelines.inc"/>
        <Type Value="Include"/>
      </Item482>
      <Item483>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_json.inc"/>
        <Type Value="Include"/>
      </Item483>
      <Item484>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_simpletextureloader.inc"/>
        <Type Value="Include"/>
      </Item484>
      <Item485>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_skeleton.inc"/>
        <Type Value="Include"/>
      </Item485>
      <Item486>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_skins.inc"/>
        <Type Value="Include"/>
      </Item486>
      <Item487>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_slots.inc"/>
        <Type Value="Include"/>
      </Item487>
      <Item488>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_slottimelines.inc"/>
        <Type Value="Include"/>
      </Item488>
      <Item489>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_textureloader.inc"/>
        <Type Value="Include"/>
      </Item489>
      <Item490>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_url.inc"/>
        <Type Value="Include"/>
      </Item490>
      <Item491>
        <Filename Value="../src/scene/load/spine/x3dloadinternalspine_weightedmeshtimelines.inc"/>
        <Type Value="Include"/>
      </Item491>
      <Item492>
        <Filename Value="../src/scene/load/x3dload.pas"/>
        <UnitName Value="X3DLoad"/>
      </Item492>
      <Item493>
        <Filename Value="../src/scene/load/x3dloadinternal3ds.pas"/>
        <UnitName Value="X3DLoadInternal3DS"/>
      </Item493>
      <Item494>
        <Filename Value="../src/scene/load/x3dloadinternalcocos2d.pas"/>
        <UnitName Value="X3DLoadInternalCocos2d"/>
      </Item494>
      <Item495>
        <Filename Value="../src/scene/load/x3dloadinternalgeo.pas"/>
        <UnitName Value="X3DLoadInternalGEO"/>
      </Item495>
      <Item496>
        <Filename Value="../src/scene/load/x3dloadinternalgltf.pas"/>
        <UnitName Value="X3DLoadInternalGltf"/>
      </Item496>
      <Item497>
        <Filename Value="../src/scene/load/x3dloadinternalimage.pas"/>
        <UnitName Value="X3DLoadInternalImage"/>
      </Item497>
      <Item498>
        <Filename Value="../src/scene/load/x3dloadinternalobj.pas"/>
        <UnitName Value="X3DLoadInternalOBJ"/>
      </Item498>
      <Item499>
        <Filename Value="../src/scene/load/x3dloadinternalstl.pas"/>
        <UnitName Value="X3DLoadInternalSTL"/>
      </Item499>
      <Item500>
        <Filename Value="../src/scene/load/x3dloadinternaltiledmap.pas"/>
        <UnitName Value="X3DLoadInternalTiledMap"/>
      </Item500>
      <Item501>
        <Filename Value="../src/scene/load/x3dloadinternalutils.pas"/>
        <UnitName Value="X3DLoadInternalUtils"/>
      </Item501>
      <Item502>
        <Filename Value="../src/scene/octreeconf.inc"/>
        <Type Value="Include"/>
      </Item502>
      <Item503>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/rotate.glb.inc"/>
        <Type Value="Include"/>
      </Item503>
      <Item504>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/rotate_collider.glb.inc"/>
        <Type Value="Include"/>
      </Item504>
      <Item505>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/rotate_final.x3dv.inc"/>
        <Type Value="Include"/>
      </Item505>
      <Item506>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/scale.glb.inc"/>
        <Type Value="Include"/>
      </Item506>
      <Item507>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/scale_final.x3dv.inc"/>
        <Type Value="Include"/>
      </Item507>
      <Item508>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/translate.glb.inc"/>
        <Type Value="Include"/>
      </Item508>
      <Item509>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/translate_collider.glb.inc"/>
        <Type Value="Include"/>
      </Item509>
      <Item510>
        <Filename Value="../src/scene/transform_manipulate_data/generated-pascal/translate_final.x3dv.inc"/>
        <Type Value="Include"/>
      </Item510>
      <Item511>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_abstractvrml1camera_1.inc"/>
        <Type Value="Include"/>
      </Item511>
      <Item512>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_abstractvrml1geometry_1.inc"/>
        <Type Value="Include"/>
      </Item512>
      <Item513>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_abstractvrml1indexed_1.inc"/>
        <Type Value="Include"/>
      </Item513>
      <Item514>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_abstractvrml1separator_1.inc"/>
        <Type Value="Include"/>
      </Item514>
      <Item515>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_abstractvrml1transformation_1.inc"/>
        <Type Value="Include"/>
      </Item515>
      <Item516>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_anchor.inc"/>
        <Type Value="Include"/>
      </Item516>
      <Item517>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_appearance.inc"/>
        <Type Value="Include"/>
      </Item517>
      <Item518>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_arc2d.inc"/>
        <Type Value="Include"/>
      </Item518>
      <Item519>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_arcclose2d.inc"/>
        <Type Value="Include"/>
      </Item519>
      <Item520>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_asciitext_1.inc"/>
        <Type Value="Include"/>
      </Item520>
      <Item521>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_audioclip.inc"/>
        <Type Value="Include"/>
      </Item521>
      <Item522>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_background.inc"/>
        <Type Value="Include"/>
      </Item522>
      <Item523>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_balljoint.inc"/>
        <Type Value="Include"/>
      </Item523>
      <Item524>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_billboard.inc"/>
        <Type Value="Include"/>
      </Item524>
      <Item525>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_blendmode.inc"/>
        <Type Value="Include"/>
      </Item525>
      <Item526>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_booleanfilter.inc"/>
        <Type Value="Include"/>
      </Item526>
      <Item527>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_booleansequencer.inc"/>
        <Type Value="Include"/>
      </Item527>
      <Item528>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_booleantoggle.inc"/>
        <Type Value="Include"/>
      </Item528>
      <Item529>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_booleantrigger.inc"/>
        <Type Value="Include"/>
      </Item529>
      <Item530>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_boundedphysicsmodel.inc"/>
        <Type Value="Include"/>
      </Item530>
      <Item531>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_box.inc"/>
        <Type Value="Include"/>
      </Item531>
      <Item532>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cadassembly.inc"/>
        <Type Value="Include"/>
      </Item532>
      <Item533>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cadface.inc"/>
        <Type Value="Include"/>
      </Item533>
      <Item534>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cadlayer.inc"/>
        <Type Value="Include"/>
      </Item534>
      <Item535>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cadpart.inc"/>
        <Type Value="Include"/>
      </Item535>
      <Item536>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_circle2d.inc"/>
        <Type Value="Include"/>
      </Item536>
      <Item537>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_clipplane.inc"/>
        <Type Value="Include"/>
      </Item537>
      <Item538>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_collidableoffset.inc"/>
        <Type Value="Include"/>
      </Item538>
      <Item539>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_collidableshape.inc"/>
        <Type Value="Include"/>
      </Item539>
      <Item540>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_collision.inc"/>
        <Type Value="Include"/>
      </Item540>
      <Item541>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_collisioncollection.inc"/>
        <Type Value="Include"/>
      </Item541>
      <Item542>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_collisionsensor.inc"/>
        <Type Value="Include"/>
      </Item542>
      <Item543>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_collisionspace.inc"/>
        <Type Value="Include"/>
      </Item543>
      <Item544>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_color.inc"/>
        <Type Value="Include"/>
      </Item544>
      <Item545>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_colordamper.inc"/>
        <Type Value="Include"/>
      </Item545>
      <Item546>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_colorinterpolator.inc"/>
        <Type Value="Include"/>
      </Item546>
      <Item547>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_colorrgba.inc"/>
        <Type Value="Include"/>
      </Item547>
      <Item548>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_colorsetinterpolator.inc"/>
        <Type Value="Include"/>
      </Item548>
      <Item549>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_commonsurfaceshader.inc"/>
        <Type Value="Include"/>
      </Item549>
      <Item550>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_composedcubemaptexture.inc"/>
        <Type Value="Include"/>
      </Item550>
      <Item551>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_composedshader.inc"/>
        <Type Value="Include"/>
      </Item551>
      <Item552>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_composedtexture3d.inc"/>
        <Type Value="Include"/>
      </Item552>
      <Item553>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cone.inc"/>
        <Type Value="Include"/>
      </Item553>
      <Item554>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cone_1.inc"/>
        <Type Value="Include"/>
      </Item554>
      <Item555>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coneemitter.inc"/>
        <Type Value="Include"/>
      </Item555>
      <Item556>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_contact.inc"/>
        <Type Value="Include"/>
      </Item556>
      <Item557>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_contour2d.inc"/>
        <Type Value="Include"/>
      </Item557>
      <Item558>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_contourpolyline2d.inc"/>
        <Type Value="Include"/>
      </Item558>
      <Item559>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_converter.inc"/>
        <Type Value="Include"/>
      </Item559>
      <Item560>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coordinate.inc"/>
        <Type Value="Include"/>
      </Item560>
      <Item561>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coordinate3_1.inc"/>
        <Type Value="Include"/>
      </Item561>
      <Item562>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coordinatedamper.inc"/>
        <Type Value="Include"/>
      </Item562>
      <Item563>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coordinatedouble.inc"/>
        <Type Value="Include"/>
      </Item563>
      <Item564>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coordinateinterpolator.inc"/>
        <Type Value="Include"/>
      </Item564>
      <Item565>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_coordinateinterpolator2d.inc"/>
        <Type Value="Include"/>
      </Item565>
      <Item566>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cube_1.inc"/>
        <Type Value="Include"/>
      </Item566>
      <Item567>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cubicbezier2dorientationinterpolator.inc"/>
        <Type Value="Include"/>
      </Item567>
      <Item568>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cubicbeziercoordinateinterpolator.inc"/>
        <Type Value="Include"/>
      </Item568>
      <Item569>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cubicbezierpositioninterpolator.inc"/>
        <Type Value="Include"/>
      </Item569>
      <Item570>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cylinder.inc"/>
        <Type Value="Include"/>
      </Item570>
      <Item571>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cylinder_1.inc"/>
        <Type Value="Include"/>
      </Item571>
      <Item572>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_cylindersensor.inc"/>
        <Type Value="Include"/>
      </Item572>
      <Item573>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_directionallight.inc"/>
        <Type Value="Include"/>
      </Item573>
      <Item574>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_directionallight_1.inc"/>
        <Type Value="Include"/>
      </Item574>
      <Item575>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_disentitymanager.inc"/>
        <Type Value="Include"/>
      </Item575>
      <Item576>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_disentitytypemapping.inc"/>
        <Type Value="Include"/>
      </Item576>
      <Item577>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_disk2d.inc"/>
        <Type Value="Include"/>
      </Item577>
      <Item578>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_doubleaxishingejoint.inc"/>
        <Type Value="Include"/>
      </Item578>
      <Item579>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_easeineaseout.inc"/>
        <Type Value="Include"/>
      </Item579>
      <Item580>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_effect.inc"/>
        <Type Value="Include"/>
      </Item580>
      <Item581>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_effectpart.inc"/>
        <Type Value="Include"/>
      </Item581>
      <Item582>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_elevationgrid.inc"/>
        <Type Value="Include"/>
      </Item582>
      <Item583>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_environment.inc"/>
        <Type Value="Include"/>
      </Item583>
      <Item584>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_environmentlight.inc"/>
        <Type Value="Include"/>
      </Item584>
      <Item585>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_espdutransform.inc"/>
        <Type Value="Include"/>
      </Item585>
      <Item586>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_explosionemitter.inc"/>
        <Type Value="Include"/>
      </Item586>
      <Item587>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_extrusion.inc"/>
        <Type Value="Include"/>
      </Item587>
      <Item588>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_fillproperties.inc"/>
        <Type Value="Include"/>
      </Item588>
      <Item589>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_floatvertexattribute.inc"/>
        <Type Value="Include"/>
      </Item589>
      <Item590>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_fog.inc"/>
        <Type Value="Include"/>
      </Item590>
      <Item591>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_fogcoordinate.inc"/>
        <Type Value="Include"/>
      </Item591>
      <Item592>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_fontstyle.inc"/>
        <Type Value="Include"/>
      </Item592>
      <Item593>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_fontstyle_1.inc"/>
        <Type Value="Include"/>
      </Item593>
      <Item594>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_forcephysicsmodel.inc"/>
        <Type Value="Include"/>
      </Item594>
      <Item595>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_generatedcubemaptexture.inc"/>
        <Type Value="Include"/>
      </Item595>
      <Item596>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_generatedshadowmap.inc"/>
        <Type Value="Include"/>
      </Item596>
      <Item597>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geocoordinate.inc"/>
        <Type Value="Include"/>
      </Item597>
      <Item598>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geoelevationgrid.inc"/>
        <Type Value="Include"/>
      </Item598>
      <Item599>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geolocation.inc"/>
        <Type Value="Include"/>
      </Item599>
      <Item600>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geolod.inc"/>
        <Type Value="Include"/>
      </Item600>
      <Item601>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geometadata.inc"/>
        <Type Value="Include"/>
      </Item601>
      <Item602>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geoorigin.inc"/>
        <Type Value="Include"/>
      </Item602>
      <Item603>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geopositioninterpolator.inc"/>
        <Type Value="Include"/>
      </Item603>
      <Item604>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geoproximitysensor.inc"/>
        <Type Value="Include"/>
      </Item604>
      <Item605>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geotouchsensor.inc"/>
        <Type Value="Include"/>
      </Item605>
      <Item606>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geotransform.inc"/>
        <Type Value="Include"/>
      </Item606>
      <Item607>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_geoviewpoint.inc"/>
        <Type Value="Include"/>
      </Item607>
      <Item608>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_group.inc"/>
        <Type Value="Include"/>
      </Item608>
      <Item609>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_group_1.inc"/>
        <Type Value="Include"/>
      </Item609>
      <Item610>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_hanimdisplacer.inc"/>
        <Type Value="Include"/>
      </Item610>
      <Item611>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_hanimhumanoid.inc"/>
        <Type Value="Include"/>
      </Item611>
      <Item612>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_hanimjoint.inc"/>
        <Type Value="Include"/>
      </Item612>
      <Item613>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_hanimmotion.inc"/>
        <Type Value="Include"/>
      </Item613>
      <Item614>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_hanimsegment.inc"/>
        <Type Value="Include"/>
      </Item614>
      <Item615>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_hanimsite.inc"/>
        <Type Value="Include"/>
      </Item615>
      <Item616>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_imagebackground.inc"/>
        <Type Value="Include"/>
      </Item616>
      <Item617>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_imagecubemaptexture.inc"/>
        <Type Value="Include"/>
      </Item617>
      <Item618>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_imagetexture.inc"/>
        <Type Value="Include"/>
      </Item618>
      <Item619>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_imagetexture3d.inc"/>
        <Type Value="Include"/>
      </Item619>
      <Item620>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedfaceset.inc"/>
        <Type Value="Include"/>
      </Item620>
      <Item621>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedfaceset_1.inc"/>
        <Type Value="Include"/>
      </Item621>
      <Item622>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedlineset.inc"/>
        <Type Value="Include"/>
      </Item622>
      <Item623>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedlineset_1.inc"/>
        <Type Value="Include"/>
      </Item623>
      <Item624>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedquadset.inc"/>
        <Type Value="Include"/>
      </Item624>
      <Item625>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedtrianglefanset.inc"/>
        <Type Value="Include"/>
      </Item625>
      <Item626>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedtrianglemesh_1.inc"/>
        <Type Value="Include"/>
      </Item626>
      <Item627>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedtriangleset.inc"/>
        <Type Value="Include"/>
      </Item627>
      <Item628>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_indexedtrianglestripset.inc"/>
        <Type Value="Include"/>
      </Item628>
      <Item629>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_info_1.inc"/>
        <Type Value="Include"/>
      </Item629>
      <Item630>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_inline.inc"/>
        <Type Value="Include"/>
      </Item630>
      <Item631>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_inlineloadcontrol.inc"/>
        <Type Value="Include"/>
      </Item631>
      <Item632>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_integersequencer.inc"/>
        <Type Value="Include"/>
      </Item632>
      <Item633>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_integertrigger.inc"/>
        <Type Value="Include"/>
      </Item633>
      <Item634>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_kambiappearance.inc"/>
        <Type Value="Include"/>
      </Item634>
      <Item635>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_kambiinline.inc"/>
        <Type Value="Include"/>
      </Item635>
      <Item636>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_kambinavigationinfo.inc"/>
        <Type Value="Include"/>
      </Item636>
      <Item637>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_keysensor.inc"/>
        <Type Value="Include"/>
      </Item637>
      <Item638>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_layer.inc"/>
        <Type Value="Include"/>
      </Item638>
      <Item639>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_layerset.inc"/>
        <Type Value="Include"/>
      </Item639>
      <Item640>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_layout.inc"/>
        <Type Value="Include"/>
      </Item640>
      <Item641>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_layoutgroup.inc"/>
        <Type Value="Include"/>
      </Item641>
      <Item642>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_layoutlayer.inc"/>
        <Type Value="Include"/>
      </Item642>
      <Item643>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_linepicksensor.inc"/>
        <Type Value="Include"/>
      </Item643>
      <Item644>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_lineproperties.inc"/>
        <Type Value="Include"/>
      </Item644>
      <Item645>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_lineset.inc"/>
        <Type Value="Include"/>
      </Item645>
      <Item646>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_loadsensor.inc"/>
        <Type Value="Include"/>
      </Item646>
      <Item647>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_localfog.inc"/>
        <Type Value="Include"/>
      </Item647>
      <Item648>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_lod.inc"/>
        <Type Value="Include"/>
      </Item648>
      <Item649>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_lod_1.inc"/>
        <Type Value="Include"/>
      </Item649>
      <Item650>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_logger.inc"/>
        <Type Value="Include"/>
      </Item650>
      <Item651>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_material.inc"/>
        <Type Value="Include"/>
      </Item651>
      <Item652>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_material_1.inc"/>
        <Type Value="Include"/>
      </Item652>
      <Item653>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_materialbinding_1.inc"/>
        <Type Value="Include"/>
      </Item653>
      <Item654>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_matrix3vertexattribute.inc"/>
        <Type Value="Include"/>
      </Item654>
      <Item655>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_matrix4vertexattribute.inc"/>
        <Type Value="Include"/>
      </Item655>
      <Item656>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_matrixtransform.inc"/>
        <Type Value="Include"/>
      </Item656>
      <Item657>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_matrixtransform_1.inc"/>
        <Type Value="Include"/>
      </Item657>
      <Item658>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_metadataboolean.inc"/>
        <Type Value="Include"/>
      </Item658>
      <Item659>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_metadatadouble.inc"/>
        <Type Value="Include"/>
      </Item659>
      <Item660>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_metadatafloat.inc"/>
        <Type Value="Include"/>
      </Item660>
      <Item661>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_metadatainteger.inc"/>
        <Type Value="Include"/>
      </Item661>
      <Item662>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_metadataset.inc"/>
        <Type Value="Include"/>
      </Item662>
      <Item663>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_metadatastring.inc"/>
        <Type Value="Include"/>
      </Item663>
      <Item664>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_motorjoint.inc"/>
        <Type Value="Include"/>
      </Item664>
      <Item665>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_movietexture.inc"/>
        <Type Value="Include"/>
      </Item665>
      <Item666>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_multigeneratedtexturecoordinate.inc"/>
        <Type Value="Include"/>
      </Item666>
      <Item667>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_multitexture.inc"/>
        <Type Value="Include"/>
      </Item667>
      <Item668>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_multitexturecoordinate.inc"/>
        <Type Value="Include"/>
      </Item668>
      <Item669>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_multitexturetransform.inc"/>
        <Type Value="Include"/>
      </Item669>
      <Item670>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_navigationinfo.inc"/>
        <Type Value="Include"/>
      </Item670>
      <Item671>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_normal.inc"/>
        <Type Value="Include"/>
      </Item671>
      <Item672>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_normalbinding_1.inc"/>
        <Type Value="Include"/>
      </Item672>
      <Item673>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_normalinterpolator.inc"/>
        <Type Value="Include"/>
      </Item673>
      <Item674>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbscurve.inc"/>
        <Type Value="Include"/>
      </Item674>
      <Item675>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbscurve2d.inc"/>
        <Type Value="Include"/>
      </Item675>
      <Item676>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbsorientationinterpolator.inc"/>
        <Type Value="Include"/>
      </Item676>
      <Item677>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbspatchsurface.inc"/>
        <Type Value="Include"/>
      </Item677>
      <Item678>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbspositioninterpolator.inc"/>
        <Type Value="Include"/>
      </Item678>
      <Item679>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbsset.inc"/>
        <Type Value="Include"/>
      </Item679>
      <Item680>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbssurfaceinterpolator.inc"/>
        <Type Value="Include"/>
      </Item680>
      <Item681>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbssweptsurface.inc"/>
        <Type Value="Include"/>
      </Item681>
      <Item682>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbsswungsurface.inc"/>
        <Type Value="Include"/>
      </Item682>
      <Item683>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbstexturecoordinate.inc"/>
        <Type Value="Include"/>
      </Item683>
      <Item684>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_nurbstrimmedsurface.inc"/>
        <Type Value="Include"/>
      </Item684>
      <Item685>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_orientationchaser.inc"/>
        <Type Value="Include"/>
      </Item685>
      <Item686>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_orientationdamper.inc"/>
        <Type Value="Include"/>
      </Item686>
      <Item687>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_orientationinterpolator.inc"/>
        <Type Value="Include"/>
      </Item687>
      <Item688>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_orientationinterpolator2d.inc"/>
        <Type Value="Include"/>
      </Item688>
      <Item689>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_orthographiccamera_1.inc"/>
        <Type Value="Include"/>
      </Item689>
      <Item690>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_orthoviewpoint.inc"/>
        <Type Value="Include"/>
      </Item690>
      <Item691>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_packagedshader.inc"/>
        <Type Value="Include"/>
      </Item691>
      <Item692>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_particlesystem.inc"/>
        <Type Value="Include"/>
      </Item692>
      <Item693>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_perspectivecamera_1.inc"/>
        <Type Value="Include"/>
      </Item693>
      <Item694>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_physicalmaterial.inc"/>
        <Type Value="Include"/>
      </Item694>
      <Item695>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pickablegroup.inc"/>
        <Type Value="Include"/>
      </Item695>
      <Item696>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pixeltexture.inc"/>
        <Type Value="Include"/>
      </Item696>
      <Item697>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pixeltexture3d.inc"/>
        <Type Value="Include"/>
      </Item697>
      <Item698>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_plane.inc"/>
        <Type Value="Include"/>
      </Item698>
      <Item699>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_planesensor.inc"/>
        <Type Value="Include"/>
      </Item699>
      <Item700>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pointemitter.inc"/>
        <Type Value="Include"/>
      </Item700>
      <Item701>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pointlight.inc"/>
        <Type Value="Include"/>
      </Item701>
      <Item702>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pointlight_1.inc"/>
        <Type Value="Include"/>
      </Item702>
      <Item703>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pointpicksensor.inc"/>
        <Type Value="Include"/>
      </Item703>
      <Item704>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pointset.inc"/>
        <Type Value="Include"/>
      </Item704>
      <Item705>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_pointset_1.inc"/>
        <Type Value="Include"/>
      </Item705>
      <Item706>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_polyline2d.inc"/>
        <Type Value="Include"/>
      </Item706>
      <Item707>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_polylineemitter.inc"/>
        <Type Value="Include"/>
      </Item707>
      <Item708>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_polypoint2d.inc"/>
        <Type Value="Include"/>
      </Item708>
      <Item709>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_positionchaser.inc"/>
        <Type Value="Include"/>
      </Item709>
      <Item710>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_positionchaser2d.inc"/>
        <Type Value="Include"/>
      </Item710>
      <Item711>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_positiondamper.inc"/>
        <Type Value="Include"/>
      </Item711>
      <Item712>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_positiondamper2d.inc"/>
        <Type Value="Include"/>
      </Item712>
      <Item713>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_positioninterpolator.inc"/>
        <Type Value="Include"/>
      </Item713>
      <Item714>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_positioninterpolator2d.inc"/>
        <Type Value="Include"/>
      </Item714>
      <Item715>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_primitivepicksensor.inc"/>
        <Type Value="Include"/>
      </Item715>
      <Item716>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_programshader.inc"/>
        <Type Value="Include"/>
      </Item716>
      <Item717>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_projectedtexturecoordinate.inc"/>
        <Type Value="Include"/>
      </Item717>
      <Item718>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_proximitysensor.inc"/>
        <Type Value="Include"/>
      </Item718>
      <Item719>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_quadset.inc"/>
        <Type Value="Include"/>
      </Item719>
      <Item720>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_receiverpdu.inc"/>
        <Type Value="Include"/>
      </Item720>
      <Item721>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_rectangle2d.inc"/>
        <Type Value="Include"/>
      </Item721>
      <Item722>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_renderedtexture.inc"/>
        <Type Value="Include"/>
      </Item722>
      <Item723>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_rigidbody.inc"/>
        <Type Value="Include"/>
      </Item723>
      <Item724>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_rigidbodycollection.inc"/>
        <Type Value="Include"/>
      </Item724>
      <Item725>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_rotation_1.inc"/>
        <Type Value="Include"/>
      </Item725>
      <Item726>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_rotationxyz_1.inc"/>
        <Type Value="Include"/>
      </Item726>
      <Item727>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_scalarchaser.inc"/>
        <Type Value="Include"/>
      </Item727>
      <Item728>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_scalarinterpolator.inc"/>
        <Type Value="Include"/>
      </Item728>
      <Item729>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_scale_1.inc"/>
        <Type Value="Include"/>
      </Item729>
      <Item730>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_screeneffect.inc"/>
        <Type Value="Include"/>
      </Item730>
      <Item731>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_screenfontstyle.inc"/>
        <Type Value="Include"/>
      </Item731>
      <Item732>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_screengroup.inc"/>
        <Type Value="Include"/>
      </Item732>
      <Item733>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_script.inc"/>
        <Type Value="Include"/>
      </Item733>
      <Item734>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_separator_1.inc"/>
        <Type Value="Include"/>
      </Item734>
      <Item735>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_shaderpart.inc"/>
        <Type Value="Include"/>
      </Item735>
      <Item736>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_shaderprogram.inc"/>
        <Type Value="Include"/>
      </Item736>
      <Item737>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_shadertexture.inc"/>
        <Type Value="Include"/>
      </Item737>
      <Item738>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_shape.inc"/>
        <Type Value="Include"/>
      </Item738>
      <Item739>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_shapehints_1.inc"/>
        <Type Value="Include"/>
      </Item739>
      <Item740>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_signalpdu.inc"/>
        <Type Value="Include"/>
      </Item740>
      <Item741>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_singleaxishingejoint.inc"/>
        <Type Value="Include"/>
      </Item741>
      <Item742>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_sliderjoint.inc"/>
        <Type Value="Include"/>
      </Item742>
      <Item743>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_sound.inc"/>
        <Type Value="Include"/>
      </Item743>
      <Item744>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_sphere.inc"/>
        <Type Value="Include"/>
      </Item744>
      <Item745>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_sphere_1.inc"/>
        <Type Value="Include"/>
      </Item745>
      <Item746>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_spheresensor.inc"/>
        <Type Value="Include"/>
      </Item746>
      <Item747>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_splinepositioninterpolator.inc"/>
        <Type Value="Include"/>
      </Item747>
      <Item748>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_splinepositioninterpolator2d.inc"/>
        <Type Value="Include"/>
      </Item748>
      <Item749>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_splinescalarinterpolator.inc"/>
        <Type Value="Include"/>
      </Item749>
      <Item750>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_spotlight.inc"/>
        <Type Value="Include"/>
      </Item750>
      <Item751>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_spotlight_1.inc"/>
        <Type Value="Include"/>
      </Item751>
      <Item752>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_squadorientationinterpolator.inc"/>
        <Type Value="Include"/>
      </Item752>
      <Item753>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_staticgroup.inc"/>
        <Type Value="Include"/>
      </Item753>
      <Item754>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_stringsensor.inc"/>
        <Type Value="Include"/>
      </Item754>
      <Item755>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_surfaceemitter.inc"/>
        <Type Value="Include"/>
      </Item755>
      <Item756>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_switch.inc"/>
        <Type Value="Include"/>
      </Item756>
      <Item757>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_switch_1.inc"/>
        <Type Value="Include"/>
      </Item757>
      <Item758>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_tangent.inc"/>
        <Type Value="Include"/>
      </Item758>
      <Item759>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_teapot.inc"/>
        <Type Value="Include"/>
      </Item759>
      <Item760>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texcoorddamper2d.inc"/>
        <Type Value="Include"/>
      </Item760>
      <Item761>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_text.inc"/>
        <Type Value="Include"/>
      </Item761>
      <Item762>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_text3d.inc"/>
        <Type Value="Include"/>
      </Item762>
      <Item763>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texture2_1.inc"/>
        <Type Value="Include"/>
      </Item763>
      <Item764>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texture2transform_1.inc"/>
        <Type Value="Include"/>
      </Item764>
      <Item765>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturebackground.inc"/>
        <Type Value="Include"/>
      </Item765>
      <Item766>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturecoordinate.inc"/>
        <Type Value="Include"/>
      </Item766>
      <Item767>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturecoordinate2_1.inc"/>
        <Type Value="Include"/>
      </Item767>
      <Item768>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturecoordinate3d.inc"/>
        <Type Value="Include"/>
      </Item768>
      <Item769>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturecoordinate4d.inc"/>
        <Type Value="Include"/>
      </Item769>
      <Item770>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturecoordinategenerator.inc"/>
        <Type Value="Include"/>
      </Item770>
      <Item771>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_textureprojector.inc"/>
        <Type Value="Include"/>
      </Item771>
      <Item772>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_textureprojectorparallel.inc"/>
        <Type Value="Include"/>
      </Item772>
      <Item773>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_textureproperties.inc"/>
        <Type Value="Include"/>
      </Item773>
      <Item774>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturetransform.inc"/>
        <Type Value="Include"/>
      </Item774>
      <Item775>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturetransform3d.inc"/>
        <Type Value="Include"/>
      </Item775>
      <Item776>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_texturetransformmatrix3d.inc"/>
        <Type Value="Include"/>
      </Item776>
      <Item777>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_timesensor.inc"/>
        <Type Value="Include"/>
      </Item777>
      <Item778>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_timetrigger.inc"/>
        <Type Value="Include"/>
      </Item778>
      <Item779>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_toggler.inc"/>
        <Type Value="Include"/>
      </Item779>
      <Item780>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_touchsensor.inc"/>
        <Type Value="Include"/>
      </Item780>
      <Item781>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_transform.inc"/>
        <Type Value="Include"/>
      </Item781>
      <Item782>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_transform_1.inc"/>
        <Type Value="Include"/>
      </Item782>
      <Item783>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_transformsensor.inc"/>
        <Type Value="Include"/>
      </Item783>
      <Item784>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_transformseparator_1.inc"/>
        <Type Value="Include"/>
      </Item784>
      <Item785>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_translation_1.inc"/>
        <Type Value="Include"/>
      </Item785>
      <Item786>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_transmitterpdu.inc"/>
        <Type Value="Include"/>
      </Item786>
      <Item787>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_trianglefanset.inc"/>
        <Type Value="Include"/>
      </Item787>
      <Item788>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_triangleset.inc"/>
        <Type Value="Include"/>
      </Item788>
      <Item789>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_triangleset2d.inc"/>
        <Type Value="Include"/>
      </Item789>
      <Item790>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_trianglestripset.inc"/>
        <Type Value="Include"/>
      </Item790>
      <Item791>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_twosidedmaterial.inc"/>
        <Type Value="Include"/>
      </Item791>
      <Item792>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_universaljoint.inc"/>
        <Type Value="Include"/>
      </Item792>
      <Item793>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_unlitmaterial.inc"/>
        <Type Value="Include"/>
      </Item793>
      <Item794>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_valuetrigger.inc"/>
        <Type Value="Include"/>
      </Item794>
      <Item795>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_vectorinterpolator.inc"/>
        <Type Value="Include"/>
      </Item795>
      <Item796>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_viewpoint.inc"/>
        <Type Value="Include"/>
      </Item796>
      <Item797>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_viewpointgroup.inc"/>
        <Type Value="Include"/>
      </Item797>
      <Item798>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_viewpointmirror.inc"/>
        <Type Value="Include"/>
      </Item798>
      <Item799>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_viewport.inc"/>
        <Type Value="Include"/>
      </Item799>
      <Item800>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_visibilitysensor.inc"/>
        <Type Value="Include"/>
      </Item800>
      <Item801>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_volumeemitter.inc"/>
        <Type Value="Include"/>
      </Item801>
      <Item802>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_volumepicksensor.inc"/>
        <Type Value="Include"/>
      </Item802>
      <Item803>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_windphysicsmodel.inc"/>
        <Type Value="Include"/>
      </Item803>
      <Item804>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_worldinfo.inc"/>
        <Type Value="Include"/>
      </Item804>
      <Item805>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_wwwanchor_1.inc"/>
        <Type Value="Include"/>
      </Item805>
      <Item806>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_wwwinline_1.inc"/>
        <Type Value="Include"/>
      </Item806>
      <Item807>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3d3dbackgroundnode.inc"/>
        <Type Value="Include"/>
      </Item807>
      <Item808>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dappearancechildnode.inc"/>
        <Type Value="Include"/>
      </Item808>
      <Item809>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dappearancenode.inc"/>
        <Type Value="Include"/>
      </Item809>
      <Item810>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dbackgroundnode.inc"/>
        <Type Value="Include"/>
      </Item810>
      <Item811>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dbindablenode.inc"/>
        <Type Value="Include"/>
      </Item811>
      <Item812>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dchasernode.inc"/>
        <Type Value="Include"/>
      </Item812>
      <Item813>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dchildnode.inc"/>
        <Type Value="Include"/>
      </Item813>
      <Item814>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dcolornode.inc"/>
        <Type Value="Include"/>
      </Item814>
      <Item815>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dcomposedgeometrynode.inc"/>
        <Type Value="Include"/>
      </Item815>
      <Item816>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dcoordinatenode.inc"/>
        <Type Value="Include"/>
      </Item816>
      <Item817>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dcubicbezierinterpolator.inc"/>
        <Type Value="Include"/>
      </Item817>
      <Item818>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3ddampernode.inc"/>
        <Type Value="Include"/>
      </Item818>
      <Item819>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3ddirectionallightnode.inc"/>
        <Type Value="Include"/>
      </Item819>
      <Item820>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3ddragsensornode.inc"/>
        <Type Value="Include"/>
      </Item820>
      <Item821>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3denvironmentalsensornode.inc"/>
        <Type Value="Include"/>
      </Item821>
      <Item822>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3denvironmenttexturenode.inc"/>
        <Type Value="Include"/>
      </Item822>
      <Item823>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dfollowernode.inc"/>
        <Type Value="Include"/>
      </Item823>
      <Item824>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dfontstylenode.inc"/>
        <Type Value="Include"/>
      </Item824>
      <Item825>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dgeometricpropertynode.inc"/>
        <Type Value="Include"/>
      </Item825>
      <Item826>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dgeometrynode.inc"/>
        <Type Value="Include"/>
      </Item826>
      <Item827>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dgroupingnode.inc"/>
        <Type Value="Include"/>
      </Item827>
      <Item828>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dinfonode.inc"/>
        <Type Value="Include"/>
      </Item828>
      <Item829>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dinterpolatornode.inc"/>
        <Type Value="Include"/>
      </Item829>
      <Item830>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dkeydevicesensornode.inc"/>
        <Type Value="Include"/>
      </Item830>
      <Item831>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dlayernode.inc"/>
        <Type Value="Include"/>
      </Item831>
      <Item832>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dlayoutnode.inc"/>
        <Type Value="Include"/>
      </Item832>
      <Item833>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dlightnode.inc"/>
        <Type Value="Include"/>
      </Item833>
      <Item834>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dmaterialnode.inc"/>
        <Type Value="Include"/>
      </Item834>
      <Item835>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dmetadatanode.inc"/>
        <Type Value="Include"/>
      </Item835>
      <Item836>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnbodycollidablenode.inc"/>
        <Type Value="Include"/>
      </Item836>
      <Item837>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnbodycollisionspacenode.inc"/>
        <Type Value="Include"/>
      </Item837>
      <Item838>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnetworksensornode.inc"/>
        <Type Value="Include"/>
      </Item838>
      <Item839>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnode.inc"/>
        <Type Value="Include"/>
      </Item839>
      <Item840>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnormalnode.inc"/>
        <Type Value="Include"/>
      </Item840>
      <Item841>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnurbscontrolcurvenode.inc"/>
        <Type Value="Include"/>
      </Item841>
      <Item842>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dnurbssurfacegeometrynode.inc"/>
        <Type Value="Include"/>
      </Item842>
      <Item843>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3donesidedmaterialnode.inc"/>
        <Type Value="Include"/>
      </Item843>
      <Item844>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dparametricgeometrynode.inc"/>
        <Type Value="Include"/>
      </Item844>
      <Item845>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dparticleemitternode.inc"/>
        <Type Value="Include"/>
      </Item845>
      <Item846>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dparticlephysicsmodelnode.inc"/>
        <Type Value="Include"/>
      </Item846>
      <Item847>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dpicksensornode.inc"/>
        <Type Value="Include"/>
      </Item847>
      <Item848>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dpointingdevicesensornode.inc"/>
        <Type Value="Include"/>
      </Item848>
      <Item849>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dpointlightnode.inc"/>
        <Type Value="Include"/>
      </Item849>
      <Item850>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dpositionallightnode.inc"/>
        <Type Value="Include"/>
      </Item850>
      <Item851>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dproductstructurechildnode.inc"/>
        <Type Value="Include"/>
      </Item851>
      <Item852>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dpunctuallightnode.inc"/>
        <Type Value="Include"/>
      </Item852>
      <Item853>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3drigidjointnode.inc"/>
        <Type Value="Include"/>
      </Item853>
      <Item854>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dscriptnode.inc"/>
        <Type Value="Include"/>
      </Item854>
      <Item855>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsensornode.inc"/>
        <Type Value="Include"/>
      </Item855>
      <Item856>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsequencernode.inc"/>
        <Type Value="Include"/>
      </Item856>
      <Item857>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dshadernode.inc"/>
        <Type Value="Include"/>
      </Item857>
      <Item858>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dshapenode.inc"/>
        <Type Value="Include"/>
      </Item858>
      <Item859>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsingletexturecoordinatenode.inc"/>
        <Type Value="Include"/>
      </Item859>
      <Item860>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsingletexturenode.inc"/>
        <Type Value="Include"/>
      </Item860>
      <Item861>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsingletexturetransformnode.inc"/>
        <Type Value="Include"/>
      </Item861>
      <Item862>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsoundnode.inc"/>
        <Type Value="Include"/>
      </Item862>
      <Item863>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dsoundsourcenode.inc"/>
        <Type Value="Include"/>
      </Item863>
      <Item864>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtexture2dnode.inc"/>
        <Type Value="Include"/>
      </Item864>
      <Item865>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtexture3dnode.inc"/>
        <Type Value="Include"/>
      </Item865>
      <Item866>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtexturecoordinatenode.inc"/>
        <Type Value="Include"/>
      </Item866>
      <Item867>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtexturenode.inc"/>
        <Type Value="Include"/>
      </Item867>
      <Item868>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtextureprojectornode.inc"/>
        <Type Value="Include"/>
      </Item868>
      <Item869>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtexturetransformnode.inc"/>
        <Type Value="Include"/>
      </Item869>
      <Item870>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtimedependentnode.inc"/>
        <Type Value="Include"/>
      </Item870>
      <Item871>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtouchsensornode.inc"/>
        <Type Value="Include"/>
      </Item871>
      <Item872>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dtriggernode.inc"/>
        <Type Value="Include"/>
      </Item872>
      <Item873>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dvertexattributenode.inc"/>
        <Type Value="Include"/>
      </Item873>
      <Item874>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dviewpointnode.inc"/>
        <Type Value="Include"/>
      </Item874>
      <Item875>
        <Filename Value="../src/scene/x3d/auto_generated_node_helpers/x3dnodes_x3dviewportnode.inc"/>
        <Type Value="Include"/>
      </Item875>
      <Item876>
        <Filename Value="../src/scene/x3d/auto_generated_teapot/teapot.inc"/>
        <Type Value="Include"/>
      </Item876>
      <Item877>
        <Filename Value="../src/scene/x3d/castlefields_internalglobals.inc"/>
        <Type Value="Include"/>
      </Item877>
      <Item878>
        <Filename Value="../src/scene/x3d/castlefields_miscglobals.inc"/>
        <Type Value="Include"/>
      </Item878>
      <Item879>
        <Filename Value="../src/scene/x3d/castlefields_misctypes.inc"/>
        <Type Value="Include"/>
      </Item879>
      <Item880>
        <Filename Value="../src/scene/x3d/castlefields_x3devent.inc"/>
        <Type Value="Include"/>
      </Item880>
      <Item881>
        <Filename Value="../src/scene/x3d/castlefields_x3devent_descendants.inc"/>
        <Type Value="Include"/>
      </Item881>
      <Item882>
        <Filename Value="../src/scene/x3d/castlefields_x3dfield.inc"/>
        <Type Value="Include"/>
      </Item882>
      <Item883>
        <Filename Value="../src/scene/x3d/castlefields_x3dfieldorevent.inc"/>
        <Type Value="Include"/>
      </Item883>
      <Item884>
        <Filename Value="../src/scene/x3d/castlefields_x3dfieldsmanager.inc"/>
        <Type Value="Include"/>
      </Item884>
      <Item885>
        <Filename Value="../src/scene/x3d/castlefields_x3dfileitem.inc"/>
        <Type Value="Include"/>
      </Item885>
      <Item886>
        <Filename Value="../src/scene/x3d/castlefields_x3dmultfield.inc"/>
        <Type Value="Include"/>
      </Item886>
      <Item887>
        <Filename Value="../src/scene/x3d/castlefields_x3dreader.inc"/>
        <Type Value="Include"/>
      </Item887>
      <Item888>
        <Filename Value="../src/scene/x3d/castlefields_x3dsimplemultfield.inc"/>
        <Type Value="Include"/>
      </Item888>
      <Item889>
        <Filename Value="../src/scene/x3d/castlefields_x3dsimplemultfield_descendants.inc"/>
        <Type Value="Include"/>
      </Item889>
      <Item890>
        <Filename Value="../src/scene/x3d/castlefields_x3dsinglefield.inc"/>
        <Type Value="Include"/>
      </Item890>
      <Item891>
        <Filename Value="../src/scene/x3d/castlefields_x3dsinglefield_descendants.inc"/>
        <Type Value="Include"/>
      </Item891>
      <Item892>
        <Filename Value="../src/scene/x3d/castlefields_x3dwriter.inc"/>
        <Type Value="Include"/>
      </Item892>
      <Item893>
        <Filename Value="../src/scene/x3d/castleinternalnodesunsupported.pas"/>
        <UnitName Value="CastleInternalNodesUnsupported"/>
      </Item893>
      <Item894>
        <Filename Value="../src/scene/x3d/x3dfields.pas"/>
        <UnitName Value="X3DFields"/>
      </Item894>
      <Item895>
        <Filename Value="../src/scene/x3d/x3dnodes.pas"/>
        <UnitName Value="X3DNodes"/>
      </Item895>
      <Item896>
        <Filename Value="../src/scene/x3d/x3dnodes_1.inc"/>
        <Type Value="Include"/>
      </Item896>
      <Item897>
        <Filename Value="../src/scene/x3d/x3dnodes_97_hanim.inc"/>
        <Type Value="Include"/>
      </Item897>
      <Item898>
        <Filename Value="../src/scene/x3d/x3dnodes_boundingboxes.inc"/>
        <Type Value="Include"/>
      </Item898>
      <Item899>
        <Filename Value="../src/scene/x3d/x3dnodes_castle.inc"/>
        <Type Value="Include"/>
      </Item899>
      <Item900>
        <Filename Value="../src/scene/x3d/x3dnodes_clipplane.inc"/>
        <Type Value="Include"/>
      </Item900>
      <Item901>
        <Filename Value="../src/scene/x3d/x3dnodes_coordpolygons.inc"/>
        <Type Value="Include"/>
      </Item901>
      <Item902>
        <Filename Value="../src/scene/x3d/x3dnodes_destructionnotification.inc"/>
        <Type Value="Include"/>
      </Item902>
      <Item903>
        <Filename Value="../src/scene/x3d/x3dnodes_encoding_classic.inc"/>
        <Type Value="Include"/>
      </Item903>
      <Item904>
        <Filename Value="../src/scene/x3d/x3dnodes_encoding_xml.inc"/>
        <Type Value="Include"/>
      </Item904>
      <Item905>
        <Filename Value="../src/scene/x3d/x3dnodes_eventsengine.inc"/>
        <Type Value="Include"/>
      </Item905>
      <Item906>
        <Filename Value="../src/scene/x3d/x3dnodes_generatedtextures.inc"/>
        <Type Value="Include"/>
      </Item906>
      <Item907>
        <Filename Value="../src/scene/x3d/x3dnodes_importexport.inc"/>
        <Type Value="Include"/>
      </Item907>
      <Item908>
        <Filename Value="../src/scene/x3d/x3dnodes_initial_types.inc"/>
        <Type Value="Include"/>
      </Item908>
      <Item909>
        <Filename Value="../src/scene/x3d/x3dnodes_instantreality.inc"/>
        <Type Value="Include"/>
      </Item909>
      <Item910>
        <Filename Value="../src/scene/x3d/x3dnodes_inventor.inc"/>
        <Type Value="Include"/>
      </Item910>
      <Item911>
        <Filename Value="../src/scene/x3d/x3dnodes_lightinstance.inc"/>
        <Type Value="Include"/>
      </Item911>
      <Item912>
        <Filename Value="../src/scene/x3d/x3dnodes_load.inc"/>
        <Type Value="Include"/>
      </Item912>
      <Item913>
        <Filename Value="../src/scene/x3d/x3dnodes_mfnode.inc"/>
        <Type Value="Include"/>
      </Item913>
      <Item914>
        <Filename Value="../src/scene/x3d/x3dnodes_miscellaneous_globals.inc"/>
        <Type Value="Include"/>
      </Item914>
      <Item915>
        <Filename Value="../src/scene/x3d/x3dnodes_miscellaneous_internals.inc"/>
        <Type Value="Include"/>
      </Item915>
      <Item916>
        <Filename Value="../src/scene/x3d/x3dnodes_names.inc"/>
        <Type Value="Include"/>
      </Item916>
      <Item917>
        <Filename Value="../src/scene/x3d/x3dnodes_nodesmanager.inc"/>
        <Type Value="Include"/>
      </Item917>
      <Item918>
        <Filename Value="../src/scene/x3d/x3dnodes_prototypes.inc"/>
        <Type Value="Include"/>
      </Item918>
      <Item919>
        <Filename Value="../src/scene/x3d/x3dnodes_save.inc"/>
        <Type Value="Include"/>
      </Item919>
      <Item920>
        <Filename Value="../src/scene/x3d/x3dnodes_sfnode.inc"/>
        <Type Value="Include"/>
      </Item920>
      <Item921>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_cadgeometry.inc"/>
        <Type Value="Include"/>
      </Item921>
      <Item922>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_core.inc"/>
        <Type Value="Include"/>
      </Item922>
      <Item923>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_cubemaptexturing.inc"/>
        <Type Value="Include"/>
      </Item923>
      <Item924>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_dis.inc"/>
        <Type Value="Include"/>
      </Item924>
      <Item925>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_environmentaleffects.inc"/>
        <Type Value="Include"/>
      </Item925>
      <Item926>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_environmentalsensor.inc"/>
        <Type Value="Include"/>
      </Item926>
      <Item927>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_eventutilities.inc"/>
        <Type Value="Include"/>
      </Item927>
      <Item928>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_followers.inc"/>
        <Type Value="Include"/>
      </Item928>
      <Item929>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_geometry2d.inc"/>
        <Type Value="Include"/>
      </Item929>
      <Item930>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_geometry3d.inc"/>
        <Type Value="Include"/>
      </Item930>
      <Item931>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_geospatial.inc"/>
        <Type Value="Include"/>
      </Item931>
      <Item932>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_grouping.inc"/>
        <Type Value="Include"/>
      </Item932>
      <Item933>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_h-anim.inc"/>
        <Type Value="Include"/>
      </Item933>
      <Item934>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_interpolation.inc"/>
        <Type Value="Include"/>
      </Item934>
      <Item935>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_interpolation_cubic_bezier.inc"/>
        <Type Value="Include"/>
      </Item935>
      <Item936>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_keydevicesensor.inc"/>
        <Type Value="Include"/>
      </Item936>
      <Item937>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_layering.inc"/>
        <Type Value="Include"/>
      </Item937>
      <Item938>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_layout.inc"/>
        <Type Value="Include"/>
      </Item938>
      <Item939>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_lighting.inc"/>
        <Type Value="Include"/>
      </Item939>
      <Item940>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_navigation.inc"/>
        <Type Value="Include"/>
      </Item940>
      <Item941>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_networking.inc"/>
        <Type Value="Include"/>
      </Item941>
      <Item942>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_nurbs.inc"/>
        <Type Value="Include"/>
      </Item942>
      <Item943>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_particlesystems.inc"/>
        <Type Value="Include"/>
      </Item943>
      <Item944>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_picking.inc"/>
        <Type Value="Include"/>
      </Item944>
      <Item945>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_pointingdevicesensor.inc"/>
        <Type Value="Include"/>
      </Item945>
      <Item946>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_rendering.inc"/>
        <Type Value="Include"/>
      </Item946>
      <Item947>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_rigidbodyphysics.inc"/>
        <Type Value="Include"/>
      </Item947>
      <Item948>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_scripting.inc"/>
        <Type Value="Include"/>
      </Item948>
      <Item949>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_shaders.inc"/>
        <Type Value="Include"/>
      </Item949>
      <Item950>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_shape.inc"/>
        <Type Value="Include"/>
      </Item950>
      <Item951>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_sound.inc"/>
        <Type Value="Include"/>
      </Item951>
      <Item952>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_text.inc"/>
        <Type Value="Include"/>
      </Item952>
      <Item953>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_textureprojector.inc"/>
        <Type Value="Include"/>
      </Item953>
      <Item954>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_texturing.inc"/>
        <Type Value="Include"/>
      </Item954>
      <Item955>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_texturing3d.inc"/>
        <Type Value="Include"/>
      </Item955>
      <Item956>
        <Filename Value="../src/scene/x3d/x3dnodes_standard_time.inc"/>
        <Type Value="Include"/>
      </Item956>
      <Item957>
        <Filename Value="../src/scene/x3d/x3dnodes_utils_box.inc"/>
        <Type Value="Include"/>
      </Item957>
      <Item958>
        <Filename Value="../src/scene/x3d/x3dnodes_utils_cone_cylinder.inc"/>
        <Type Value="Include"/>
      </Item958>
      <Item959>
        <Filename Value="../src/scene/x3d/x3dnodes_utils_elevationgrid.inc"/>
        <Type Value="Include"/>
      </Item959>
      <Item960>
        <Filename Value="../src/scene/x3d/x3dnodes_utils_extrusion.inc"/>
        <Type Value="Include"/>
      </Item960>
      <Item961>
        <Filename Value="../src/scene/x3d/x3dnodes_utils_materials.inc"/>
        <Type Value="Include"/>
      </Item961>
      <Item962>
        <Filename Value="../src/scene/x3d/x3dnodes_utils_sphere.inc"/>
        <Type Value="Include"/>
      </Item962>
      <Item963>
        <Filename Value="../src/scene/x3d/x3dnodes_verticesandtrianglescounting.inc"/>
        <Type Value="Include"/>
      </Item963>
      <Item964>
        <Filename Value="../src/scene/x3d/x3dnodes_vrml1state.inc"/>
        <Type Value="Include"/>
      </Item964>
      <Item965>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dfonttexturescache.inc"/>
        <Type Value="Include"/>
      </Item965>
      <Item966>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dgraphtraversestate.inc"/>
        <Type Value="Include"/>
      </Item966>
      <Item967>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dinterfacedeclaration.inc"/>
        <Type Value="Include"/>
      </Item967>
      <Item968>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dnode.inc"/>
        <Type Value="Include"/>
      </Item968>
      <Item969>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dnodeclasseslist.inc"/>
        <Type Value="Include"/>
      </Item969>
      <Item970>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dnodescache.inc"/>
        <Type Value="Include"/>
      </Item970>
      <Item971>
        <Filename Value="../src/scene/x3d/x3dnodes_x3droute.inc"/>
        <Type Value="Include"/>
      </Item971>
      <Item972>
        <Filename Value="../src/scene/x3d/x3dnodes_x3dunknownnode.inc"/>
        <Type Value="Include"/>
      </Item972>
      <Item973>
        <Filename Value="../src/scene/x3dcamerautils.pas"/>
        <UnitName Value="X3DCameraUtils"/>
      </Item973>
      <Item974>
        <Filename Value="../src/scene/x3dtime.pas"/>
        <UnitName Value="X3DTime"/>
      </Item974>
      <Item975>
        <Filename Value="../src/services/castleactivityrecognition.pas"/>
        <UnitName Value="CastleActivityRecognition"/>
      </Item975>
      <Item976>
        <Filename Value="../src/services/castleads.pas"/>
        <UnitName Value="CastleAds"/>
      </Item976>
      <Item977>
        <Filename Value="../src/services/castleanalytics.pas"/>
        <UnitName Value="CastleAnalytics"/>
      </Item977>
      <Item978>
        <Filename Value="../src/services/castlefacebook.pas"/>
        <UnitName Value="CastleFacebook"/>
      </Item978>
      <Item979>
        <Filename Value="../src/services/castlegameservice.pas"/>
        <UnitName Value="CastleGameService"/>
      </Item979>
      <Item980>
        <Filename Value="../src/services/castlehelpshift.pas"/>
        <UnitName Value="CastleHelpshift"/>
      </Item980>
      <Item981>
        <Filename Value="../src/services/castleinapppurchases.pas"/>
        <UnitName Value="CastleInAppPurchases"/>
      </Item981>
      <Item982>
        <Filename Value="../src/services/castleopendocument.pas"/>
        <UnitName Value="CastleOpenDocument"/>
      </Item982>
      <Item983>
        <Filename Value="../src/services/castleopendocument_freedesktop.inc"/>
        <Type Value="Include"/>
      </Item983>
      <Item984>
        <Filename Value="../src/services/castleopendocument_macos.inc"/>
        <Type Value="Include"/>
      </Item984>
      <Item985>
        <Filename Value="../src/services/castleopendocument_messaging.inc"/>
        <Type Value="Include"/>
      </Item985>
      <Item986>
        <Filename Value="../src/services/castleopendocument_open.inc"/>
        <Type Value="Include"/>
      </Item986>
      <Item987>
        <Filename Value="../src/services/castleopendocument_process.inc"/>
        <Type Value="Include"/>
      </Item987>
      <Item988>
        <Filename Value="../src/services/castleopendocument_shellexecute.inc"/>
        <Type Value="Include"/>
      </Item988>
      <Item989>
        <Filename Value="../src/services/castleopendocument_unimplemented.inc"/>
        <Type Value="Include"/>
      </Item989>
      <Item990>
        <Filename Value="../src/services/castlephotoservice.pas"/>
        <UnitName Value="CastlePhotoService"/>
      </Item990>
      <Item991>
        <Filename Value="../src/services/castletenjin.pas"/>
        <UnitName Value="CastleTenjin"/>
      </Item991>
      <Item992>
        <Filename Value="../src/services/castletestfairy.pas"/>
        <UnitName Value="CastleTestFairy"/>
      </Item992>
      <Item993>
        <Filename Value="../src/services/steam/castleinternalsteamapi.pas"/>
        <UnitName Value="CastleInternalSteamApi"/>
      </Item993>
      <Item994>
        <Filename Value="../src/services/steam/castlesteam.pas"/>
        <UnitName Value="CastleSteam"/>
      </Item994>
      <Item995>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastleballjoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item995>
      <Item996>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlebillboard_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item996>
      <Item997>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastleboxcollider_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item997>
      <Item998>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlecamera_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item998>
      <Item999>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlecollider_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item999>
      <Item1000>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastledistancejoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1000>
      <Item1001>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlefixedjoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1001>
      <Item1002>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlegrabjoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1002>
      <Item1003>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlehingejoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1003>
      <Item1004>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastleorthographic_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1004>
      <Item1005>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastleplanecollider_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1005>
      <Item1006>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlepulleyjoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1006>
      <Item1007>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlerigidbody_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1007>
      <Item1008>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastleropejoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1008>
      <Item1009>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlesliderjoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1009>
      <Item1010>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastlesticktosurface_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1010>
      <Item1011>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastletransform_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1011>
      <Item1012>
        <Filename Value="../src/transform/auto_generated_persistent_vectors/tcastleworldplanedistancejoint_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1012>
      <Item1013>
        <Filename Value="../src/transform/castlebehaviors.pas"/>
        <UnitName Value="CastleBehaviors"/>
      </Item1013>
      <Item1014>
        <Filename Value="../src/transform/castlebehaviors_billboard.inc"/>
        <Type Value="Include"/>
      </Item1014>
      <Item1015>
        <Filename Value="../src/transform/castlebehaviors_soundsource.inc"/>
        <Type Value="Include"/>
      </Item1015>
      <Item1016>
        <Filename Value="../src/transform/castlebehaviors_sticktosurface.inc"/>
        <Type Value="Include"/>
      </Item1016>
      <Item1017>
        <Filename Value="../src/transform/castleboxes.pas"/>
        <UnitName Value="CastleBoxes"/>
      </Item1017>
      <Item1018>
        <Filename Value="../src/transform/castlecameras.pas"/>
        <UnitName Value="CastleCameras"/>
      </Item1018>
      <Item1019>
        <Filename Value="../src/transform/castlefrustum.pas"/>
        <UnitName Value="CastleFrustum"/>
      </Item1019>
      <Item1020>
        <Filename Value="../src/transform/castleinternalbasetriangleoctree.pas"/>
        <UnitName Value="CastleInternalBaseTriangleOctree"/>
      </Item1020>
      <Item1021>
        <Filename Value="../src/transform/castleinternalbasetriangleoctree_raysegment_nonleaf.inc"/>
        <Type Value="Include"/>
      </Item1021>
      <Item1022>
        <Filename Value="../src/transform/castleinternalcubemaps.pas"/>
        <UnitName Value="CastleInternalCubeMaps"/>
      </Item1022>
      <Item1023>
        <Filename Value="../src/transform/castleinternalgeometryarrays.pas"/>
        <UnitName Value="CastleInternalGeometryArrays"/>
      </Item1023>
      <Item1024>
        <Filename Value="../src/transform/castleinternalglshadowvolumes.pas"/>
        <UnitName Value="CastleInternalGLShadowVolumes"/>
      </Item1024>
      <Item1025>
        <Filename Value="../src/transform/castleinternalnurbs.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleInternalNurbs"/>
      </Item1025>
      <Item1026>
        <Filename Value="../src/transform/castleinternaloctree.pas"/>
        <UnitName Value="CastleInternalOctree"/>
      </Item1026>
      <Item1027>
        <Filename Value="../src/transform/castleinternalphysicsvisualization.pas"/>
        <UnitName Value="CastleInternalPhysicsVisualization"/>
      </Item1027>
      <Item1028>
        <Filename Value="../src/transform/castleinternalrays.pas"/>
        <UnitName Value="CastleInternalRays"/>
      </Item1028>
      <Item1029>
        <Filename Value="../src/transform/castleinternalspacefillingcurves.pas"/>
        <UnitName Value="CastleInternalSpaceFillingCurves"/>
      </Item1029>
      <Item1030>
        <Filename Value="../src/transform/castleinternalspheresampling.pas"/>
        <UnitName Value="CastleInternalSphereSampling"/>
      </Item1030>
      <Item1031>
        <Filename Value="../src/transform/castleinternalsphericalharmonics.pas"/>
        <UnitName Value="CastleInternalSphericalHarmonics"/>
      </Item1031>
      <Item1032>
        <Filename Value="../src/transform/castlesectors.pas"/>
        <UnitName Value="CastleSectors"/>
      </Item1032>
      <Item1033>
        <Filename Value="../src/transform/castletransform.pas"/>
        <UnitName Value="CastleTransform"/>
      </Item1033>
      <Item1034>
        <Filename Value="../src/transform/castletransform_abstractroottransform.inc"/>
        <Type Value="Include"/>
      </Item1034>
      <Item1035>
        <Filename Value="../src/transform/castletransform_behavior.inc"/>
        <Type Value="Include"/>
      </Item1035>
      <Item1036>
        <Filename Value="../src/transform/castletransform_camera.inc"/>
        <Type Value="Include"/>
      </Item1036>
      <Item1037>
        <Filename Value="../src/transform/castletransform_camera_utils.inc"/>
        <Type Value="Include"/>
      </Item1037>
      <Item1038>
        <Filename Value="../src/transform/castletransform_collisions.inc"/>
        <Type Value="Include"/>
      </Item1038>
      <Item1039>
        <Filename Value="../src/transform/castletransform_design.inc"/>
        <Type Value="Include"/>
      </Item1039>
      <Item1040>
        <Filename Value="../src/transform/castletransform_initial_types.inc"/>
        <Type Value="Include"/>
      </Item1040>
      <Item1041>
        <Filename Value="../src/transform/castletransform_joints.inc"/>
        <Type Value="Include"/>
      </Item1041>
      <Item1042>
        <Filename Value="../src/transform/castletransform_joints_experimental.inc"/>
        <Type Value="Include"/>
      </Item1042>
      <Item1043>
        <Filename Value="../src/transform/castletransform_miscellaneous_globals.inc"/>
        <Type Value="Include"/>
      </Item1043>
      <Item1044>
        <Filename Value="../src/transform/castletransform_physics.inc"/>
        <Type Value="Include"/>
      </Item1044>
      <Item1045>
        <Filename Value="../src/transform/castletransform_physics_layers.inc"/>
        <Type Value="Include"/>
      </Item1045>
      <Item1046>
        <Filename Value="../src/transform/castletransform_reference.inc"/>
        <Type Value="Include"/>
      </Item1046>
      <Item1047>
        <Filename Value="../src/transform/castletransform_renderparams.inc"/>
        <Type Value="Include"/>
      </Item1047>
      <Item1048>
        <Filename Value="../src/transform/castletransform_serialize.inc"/>
        <Type Value="Include"/>
      </Item1048>
      <Item1049>
        <Filename Value="../src/transform/castletransform_transform.inc"/>
        <Type Value="Include"/>
      </Item1049>
      <Item1050>
        <Filename Value="../src/transform/castletransform_transformlist.inc"/>
        <Type Value="Include"/>
      </Item1050>
      <Item1051>
        <Filename Value="../src/transform/castletriangles.pas"/>
        <UnitName Value="CastleTriangles"/>
      </Item1051>
      <Item1052>
        <Filename Value="../src/transform/castletriangles_istrianglespherecollision.inc"/>
        <Type Value="Include"/>
      </Item1052>
      <Item1053>
        <Filename Value="../src/transform/castletriangulate.pas"/>
        <UnitName Value="CastleTriangulate"/>
      </Item1053>
      <Item1054>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastleabstractslider_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1054>
      <Item1055>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlebutton_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1055>
      <Item1056>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlecheckbox_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1056>
      <Item1057>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlecrosshair_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1057>
      <Item1058>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastleedit_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1058>
      <Item1059>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastleimagecontrol_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1059>
      <Item1060>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlelabel_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1060>
      <Item1061>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlenotifications_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1061>
      <Item1062>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlepackedgroup_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1062>
      <Item1063>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlerectanglecontrol_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1063>
      <Item1064>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastlescrollviewcustom_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1064>
      <Item1065>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastleshape_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1065>
      <Item1066>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastleuserinterface_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1066>
      <Item1067>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tcastleuserinterfacefont_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1067>
      <Item1068>
        <Filename Value="../src/ui/auto_generated_persistent_vectors/tviewdialog_persistent_vectors.inc"/>
        <Type Value="Include"/>
      </Item1068>
      <Item1069>
        <Filename Value="../src/ui/castlecontrols.pas"/>
        <UnitName Value="CastleControls"/>
      </Item1069>
      <Item1070>
        <Filename Value="../src/ui/castlecontrols_button.inc"/>
        <Type Value="Include"/>
      </Item1070>
      <Item1071>
        <Filename Value="../src/ui/castlecontrols_checkbox.inc"/>
        <Type Value="Include"/>
      </Item1071>
      <Item1072>
        <Filename Value="../src/ui/castlecontrols_clipboard.inc"/>
        <Type Value="Include"/>
      </Item1072>
      <Item1073>
        <Filename Value="../src/ui/castlecontrols_crosshair.inc"/>
        <Type Value="Include"/>
      </Item1073>
      <Item1074>
        <Filename Value="../src/ui/castlecontrols_design.inc"/>
        <Type Value="Include"/>
      </Item1074>
      <Item1075>
        <Filename Value="../src/ui/castlecontrols_edit.inc"/>
        <Type Value="Include"/>
      </Item1075>
      <Item1076>
        <Filename Value="../src/ui/castlecontrols_groups.inc"/>
        <Type Value="Include"/>
      </Item1076>
      <Item1077>
        <Filename Value="../src/ui/castlecontrols_imagecontrol.inc"/>
        <Type Value="Include"/>
      </Item1077>
      <Item1078>
        <Filename Value="../src/ui/castlecontrols_initial_types.inc"/>
        <Type Value="Include"/>
      </Item1078>
      <Item1079>
        <Filename Value="../src/ui/castlecontrols_label.inc"/>
        <Type Value="Include"/>
      </Item1079>
      <Item1080>
        <Filename Value="../src/ui/castlecontrols_mask.inc"/>
        <Type Value="Include"/>
      </Item1080>
      <Item1081>
        <Filename Value="../src/ui/castlecontrols_panel.inc"/>
        <Type Value="Include"/>
      </Item1081>
      <Item1082>
        <Filename Value="../src/ui/castlecontrols_rectanglecontrol.inc"/>
        <Type Value="Include"/>
      </Item1082>
      <Item1083>
        <Filename Value="../src/ui/castlecontrols_scrollview.inc"/>
        <Type Value="Include"/>
      </Item1083>
      <Item1084>
        <Filename Value="../src/ui/castlecontrols_shape.inc"/>
        <Type Value="Include"/>
      </Item1084>
      <Item1085>
        <Filename Value="../src/ui/castlecontrols_simplebackground.inc"/>
        <Type Value="Include"/>
      </Item1085>
      <Item1086>
        <Filename Value="../src/ui/castlecontrols_sliders.inc"/>
        <Type Value="Include"/>
      </Item1086>
      <Item1087>
        <Filename Value="../src/ui/castlecontrols_switchcontrol.inc"/>
        <Type Value="Include"/>
      </Item1087>
      <Item1088>
        <Filename Value="../src/ui/castlecontrols_timer.inc"/>
        <Type Value="Include"/>
      </Item1088>
      <Item1089>
        <Filename Value="../src/ui/castlecontrols_uifont.inc"/>
        <Type Value="Include"/>
      </Item1089>
      <Item1090>
        <Filename Value="../src/ui/castlecontrols_userinterfacefont.inc"/>
        <Type Value="Include"/>
      </Item1090>
      <Item1091>
        <Filename Value="../src/ui/castledialogviews.pas"/>
        <UnitName Value="CastleDialogViews"/>
      </Item1091>
      <Item1092>
        <Filename Value="../src/ui/castledialogviews_dialog.inc"/>
        <Type Value="Include"/>
      </Item1092>
      <Item1093>
        <Filename Value="../src/ui/castleflasheffect.pas"/>
        <UnitName Value="CastleFlashEffect"/>
      </Item1093>
      <Item1094>
        <Filename Value="../src/ui/castleinputs.pas"/>
        <UnitName Value="CastleInputs"/>
      </Item1094>
      <Item1095>
        <Filename Value="../src/ui/castleinternalcameragestures.pas"/>
        <UnitName Value="CastleInternalCameraGestures"/>
      </Item1095>
      <Item1096>
        <Filename Value="../src/ui/castleinternalcontrolsimages.pas"/>
        <UnitName Value="CastleInternalControlsImages"/>
      </Item1096>
      <Item1097>
        <Filename Value="../src/ui/castleinternalcontrolsimages.image_data"/>
        <Type Value="Text"/>
      </Item1097>
      <Item1098>
        <Filename Value="../src/ui/castleinternalinspector.pas"/>
        <UnitName Value="CastleInternalInspector"/>
      </Item1098>
      <Item1099>
        <Filename Value="../src/ui/castleinternalinspector_hierarchy.inc"/>
        <Type Value="Include"/>
      </Item1099>
      <Item1100>
        <Filename Value="../src/ui/castleinternalinspector_properties.inc"/>
        <Type Value="Include"/>
      </Item1100>
      <Item1101>
        <Filename Value="../src/ui/castleinternalgamecontrollersexplicit.pas"/>
        <UnitName Value="CastleInternalGameControllersExplicit"/>
      </Item1101>
      <Item1102>
        <Filename Value="../src/ui/castleinternalgamecontrollerslinux.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleInternalGameControllersLinux"/>
      </Item1102>
      <Item1103>
        <Filename Value="../src/ui/castleinternalgamecontrollerswindows.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleInternalGameControllersWindows"/>
      </Item1103>
      <Item1104>
        <Filename Value="../src/ui/castleinternalpk3dconnexion.pas"/>
        <UnitName Value="CastleInternalPk3DConnexion"/>
      </Item1104>
      <Item1105>
        <Filename Value="../src/ui/castleinternalsettings.pas"/>
        <UnitName Value="CastleInternalSettings"/>
      </Item1105>
      <Item1106>
        <Filename Value="../src/ui/castlegamecontrollers.pas"/>
        <UnitName Value="CastleGameControllers"/>
      </Item1106>
      <Item1107>
        <Filename Value="../src/ui/castlekeysmouse.pas"/>
        <UnitName Value="CastleKeysMouse"/>
      </Item1107>
      <Item1108>
        <Filename Value="../src/ui/castlenotifications.pas"/>
        <UnitName Value="CastleNotifications"/>
      </Item1108>
      <Item1109>
        <Filename Value="../src/ui/castleuicontrols.pas"/>
        <UnitName Value="CastleUIControls"/>
      </Item1109>
      <Item1110>
        <Filename Value="../src/ui/castleuicontrols_container.inc"/>
        <Type Value="Include"/>
      </Item1110>
      <Item1111>
        <Filename Value="../src/ui/castleuicontrols_deprecated.inc"/>
        <Type Value="Include"/>
      </Item1111>
      <Item1112>
        <Filename Value="../src/ui/castleuicontrols_initial_constants.inc"/>
        <Type Value="Include"/>
      </Item1112>
      <Item1113>
        <Filename Value="../src/ui/castleuicontrols_initial_types.inc"/>
        <Type Value="Include"/>
      </Item1113>
      <Item1114>
        <Filename Value="../src/ui/castleuicontrols_inputinspector.inc"/>
        <Type Value="Include"/>
      </Item1114>
      <Item1115>
        <Filename Value="../src/ui/castleuicontrols_internalchildrencontrols.inc"/>
        <Type Value="Include"/>
      </Item1115>
      <Item1116>
        <Filename Value="../src/ui/castleuicontrols_miscellaneous_globals.inc"/>
        <Type Value="Include"/>
      </Item1116>
      <Item1117>
        <Filename Value="../src/ui/castleuicontrols_serialize.inc"/>
        <Type Value="Include"/>
      </Item1117>
      <Item1118>
        <Filename Value="../src/ui/castleuicontrols_theme.inc"/>
        <Type Value="Include"/>
      </Item1118>
      <Item1119>
        <Filename Value="../src/ui/castleuicontrols_touchlist.inc"/>
        <Type Value="Include"/>
      </Item1119>
      <Item1120>
        <Filename Value="../src/ui/castleuicontrols_userinterface.inc"/>
        <Type Value="Include"/>
      </Item1120>
      <Item1121>
        <Filename Value="../src/ui/castleuicontrols_userinterfacelist.inc"/>
        <Type Value="Include"/>
      </Item1121>
      <Item1122>
        <Filename Value="../src/ui/castleuicontrols_view.inc"/>
        <Type Value="Include"/>
      </Item1122>
      <Item1123>
        <Filename Value="../src/ui/designs/component_properties.castle-user-interface.inc"/>
        <Type Value="Include"/>
      </Item1123>
      <Item1124>
        <Filename Value="../src/ui/designs/components_hierarchy.castle-user-interface.inc"/>
        <Type Value="Include"/>
      </Item1124>
      <Item1125>
        <Filename Value="../src/ui/designs/inspector_ui.castle-user-interface.inc"/>
        <Type Value="Include"/>
      </Item1125>
      <Item1126>
        <Filename Value="../src/ui/windows/castleinternaltdxinput_tlb.pas"/>
        <AddToUsesPkgSection Value="False"/>
        <UnitName Value="CastleInternalTDxInput_TLB"/>
      </Item1126>
      <Item1127>
        <Filename Value="../src/vampyre_imaginglib/src/Extensions/ImagingExtFileFormats.pas"/>
        <UnitName Value="ImagingExtFileFormats"/>
      </Item1127>
      <Item1128>
        <Filename Value="../src/vampyre_imaginglib/src/Source/Imaging.pas"/>
        <UnitName Value="Imaging"/>
      </Item1128>
      <Item1129>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingBitmap.pas"/>
        <UnitName Value="ImagingBitmap"/>
      </Item1129>
      <Item1130>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingCanvases.pas"/>
        <UnitName Value="ImagingCanvases"/>
      </Item1130>
      <Item1131>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingClasses.pas"/>
        <UnitName Value="ImagingClasses"/>
      </Item1131>
      <Item1132>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingColors.pas"/>
        <UnitName Value="ImagingColors"/>
      </Item1132>
      <Item1133>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingComponents.pas"/>
        <UnitName Value="ImagingComponents"/>
      </Item1133>
      <Item1134>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingDds.pas"/>
        <UnitName Value="ImagingDds"/>
      </Item1134>
      <Item1135>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingFormats.pas"/>
        <UnitName Value="ImagingFormats"/>
      </Item1135>
      <Item1136>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingGif.pas"/>
        <UnitName Value="ImagingGif"/>
      </Item1136>
      <Item1137>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingIO.pas"/>
        <UnitName Value="ImagingIO"/>
      </Item1137>
      <Item1138>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingJpeg.pas"/>
        <UnitName Value="ImagingJpeg"/>
      </Item1138>
      <Item1139>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingNetworkGraphics.pas"/>
        <UnitName Value="ImagingNetworkGraphics"/>
      </Item1139>
      <Item1140>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingOptions.inc"/>
        <Type Value="Include"/>
      </Item1140>
      <Item1141>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingPortableMaps.pas"/>
        <UnitName Value="ImagingPortableMaps"/>
      </Item1141>
      <Item1142>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingRadiance.pas"/>
        <UnitName Value="ImagingRadiance"/>
      </Item1142>
      <Item1143>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingTarga.pas"/>
        <UnitName Value="ImagingTarga"/>
      </Item1143>
      <Item1144>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingTypes.pas"/>
        <UnitName Value="ImagingTypes"/>
      </Item1144>
      <Item1145>
        <Filename Value="../src/vampyre_imaginglib/src/Source/ImagingUtility.pas"/>
        <UnitName Value="ImagingUtility"/>
      </Item1145>
      <Item1146>
        <Filename Value="../src/files/castledownload_url_castleconfig.inc"/>
        <Type Value="Include"/>
      </Item1146>
      <Item1147>
        <Filename Value="../src/files/castleuriutils_memoryfilesystem.inc"/>
        <Type Value="Include"/>
      </Item1147>
      <Item1148>
        <Filename Value="../src/services/castleopendocument_wasi.inc"/>
        <Type Value="Include"/>
      </Item1148>
      <Item1149>
        <Filename Value="../src/scene/castlerendererinternalshader_shaderlibraries.inc"/>
        <Type Value="Include"/>
      </Item1149>
      <Item1150>
        <Filename Value="../src/scene/glsl/generated-pascal/shader_libraries_EyeWorldSpace.glsl.inc"/>
        <Type Value="Include"/>
      </Item1150>
      <Item1151>
        <Filename Value="../src/scene/castleinternalprimitivematerial.pas"/>
        <UnitName Value="CastleInternalPrimitiveMaterial"/>
      </Item1151>
    </Files>
    <CompatibilityMode Value="True"/>
    <RequiredPkgs Count="1">
      <Item1>
        <PackageName Value="FCL"/>
        <MinVersion Major="1" Valid="True"/>
      </Item1>
    </RequiredPkgs>
    <UsageOptions>
      <UnitPath Value="$(PkgOutDir)"/>
    </UsageOptions>
    <PublishOptions>
      <Version Value="2"/>
    </PublishOptions>
    <CustomOptions Items="ExternHelp" Version="2">
      <_ExternHelp Items="Count"/>
    </CustomOptions>
  </Package>
</CONFIG>
